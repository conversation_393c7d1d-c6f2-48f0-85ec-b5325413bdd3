# Pantry Pal Backend

A multi-tenant pantry management application backend built with Go, following Clean Architecture principles.

## Features

* **Authentication & Authorization**: JWT-based authentication with refresh tokens and role-based access control
* **Clean Architecture**: Organized codebase following Clean Architecture principles
* **Standardized Responses**: Consistent API response format with proper error handling
* **Structured Logging**: Comprehensive logging with request tracing
* **Database Management**: PostgreSQL with GORM and database migrations
* **Configuration Management**: Flexible configuration with file and environment variable support
* **Development Tools**: Docker Compose for local development, Makefile for common tasks

## Tech Stack

* **Language**: Go 1.24+
* **Web Framework**: Fiber v2
* **Database**: PostgreSQL 15+
* **ORM**: GORM
* **Cache**: Redis 7+
* **Authentication**: JWT with refresh tokens
* **Authorization**: Casbin RBAC
* **Logging**: Zerolog
* **Configuration**: Koanf
* **Migrations**: golang-migrate
* **Validation**: go-playground/validator

## Project Structure

```
pantry-pal/
├── cmd/api/                    # Application entry point
├── internal/
│   ├── core/
│   │   ├── domain/            # Domain entities and interfaces
│   │   └── usecases/          # Business logic (to be implemented)
│   └── infra/
│       ├── auth/              # JWT authentication service
│       ├── config/            # Configuration management
│       ├── errors/            # Error handling and conventions
│       ├── logger/            # Logging setup
│       ├── persistence/       # Database layer
│       │   ├── postgres/      # PostgreSQL repositories
│       │   └── migrations/    # Database migrations
│       └── web/               # HTTP layer
│           ├── handler/       # HTTP handlers
│           └── middleware/    # HTTP middleware
├── scripts/                   # Utility scripts
├── config.yaml               # Configuration file
├── docker-compose.yml        # Development environment
├── Makefile                  # Development tasks
└── README.md
```

## Quick Start

### Prerequisites

* Go 1.24+
* Docker and Docker Compose (for database and Redis)
* Make (optional, for convenience)

### Setup Development Environment

1. **Clone the repository**
   

```bash
   git clone <repository-url>
   cd pantry-pal
   ```

2. **Install dependencies**
   

```bash
   go mod tidy
   ```

3. **Setup environment variables**
   

```bash
   cp .env.example .env
   # Edit .env with your configuration
   # Required: APP_DATABASE_PASSWORD and APP_AUTH_JWT_SECRET
   ```

4. **Start development services**
   

```bash
   # Start PostgreSQL and Redis
   docker-compose up -d postgres redis

   # Run database migrations
   make migrate-up
   ```

5. **Build and run the application**
   

```bash
   # Build the application
   go build -o bin/pantry-pal-api cmd/api/main.go

   # Run the application
   ./bin/pantry-pal-api
   # Or directly: go run cmd/api/main.go
   ```

6. **Test the setup**
   

```bash
   # Run tests
   go test ./...

   # Check health endpoint
   curl http://localhost:8080/health
   ```

The API will be available at `http://localhost:8080`

### Current Implementation Status

✅ **Completed:**
* Clean Architecture project structure
* Configuration management with Koanf (file + environment variables)
* Structured logging with Zerolog (request tracing, contextual logging)
* Standardized error handling with custom error types
* Standardized API response format with pagination support
* JWT-based authentication with refresh tokens
* User domain model with events
* Database models and repositories (GORM + PostgreSQL)
* HTTP middleware (request ID, logging, authentication)
* Basic authentication endpoints (register, login, refresh, logout)
* User profile endpoints (get, update)
* Database migrations
* Comprehensive test coverage for domain and error handling

🚧 **In Progress/TODO:**
* Authorization with Casbin RBAC
* Idempotency middleware with Redis
* Password change functionality (needs auth service injection)
* Pantry management features
* Product catalog and inventory management
* Shopping lists and recipes
* Notifications system

### Environment Variables

Required environment variables (see `.env.example` ):

* `APP_DATABASE_PASSWORD`: PostgreSQL password
* `APP_AUTH_JWT_SECRET`: JWT signing secret (use a long, random string)

Optional overrides:
* `APP_SERVER_PORT`: Server port (default: 8080)
* `APP_LOGGER_LEVEL`: Log level (default: info)
* `APP_LOGGER_FORMAT`: Log format (console/json, default: console)

## API Endpoints

### Authentication

* `POST /api/v1/auth/register` - User registration
* `POST /api/v1/auth/login` - User login
* `POST /api/v1/auth/refresh` - Refresh access token
* `POST /api/v1/auth/logout` - User logout

### User Management

* `GET /api/v1/users/profile` - Get user profile (authenticated)
* `PUT /api/v1/users/profile` - Update user profile (authenticated)
* `POST /api/v1/users/change-password` - Change password (authenticated)

### Pantry Management

* `POST /api/v1/pantries` - Create a new pantry (authenticated)
* `GET /api/v1/pantries` - Get user's pantries (authenticated)
* `GET /api/v1/pantries/:pantryId` - Get specific pantry (authenticated, member)
* `PUT /api/v1/pantries/:pantryId` - Update pantry (authenticated, editor+)
* `DELETE /api/v1/pantries/:pantryId` - Delete pantry (authenticated, owner)
* `POST /api/v1/pantries/:pantryId/transfer-ownership` - Transfer ownership (authenticated, owner)

### Pantry Membership

* `POST /api/v1/pantries/:pantryId/members` - Invite member (authenticated, admin+)
* `GET /api/v1/pantries/:pantryId/members` - Get pantry members (authenticated, member)
* `PUT /api/v1/pantries/:pantryId/members/:memberId` - Update member role (authenticated, admin+)
* `DELETE /api/v1/pantries/:pantryId/members/:memberId` - Remove member (authenticated, admin+)
* `POST /api/v1/pantries/:pantryId/leave` - Leave pantry (authenticated, member)

### Pantry Invitations

* `GET /api/v1/pantries/invitations` - Get pending invitations (authenticated)
* `POST /api/v1/pantries/invitations/accept` - Accept invitation (authenticated)
* `POST /api/v1/pantries/invitations/reject` - Reject invitation (authenticated)

### Pantry Locations

* `POST /api/v1/pantries/:pantryId/locations` - Create location (authenticated, editor+)
* `GET /api/v1/pantries/:pantryId/locations` - Get pantry locations (authenticated, member)
* `GET /api/v1/pantries/:pantryId/locations/:locationId` - Get specific location (authenticated, member)
* `PUT /api/v1/pantries/:pantryId/locations/:locationId` - Update location (authenticated, editor+)
* `DELETE /api/v1/pantries/:pantryId/locations/:locationId` - Delete location (authenticated, editor+)

### Health Check

* `GET /health` - Application health check

## API Response Format

All API responses follow a standardized format:

```json
{
  "success": true,
  "data": { ... },
  "message": "Operation successful",
  "timestamp": "2023-10-28T10:30:00Z",
  "request_id": "uuid-here"
}
```

Error responses:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": { ... }
  },
  "timestamp": "2023-10-28T10:30:00Z",
  "request_id": "uuid-here"
}
```

## Development

### Available Make Commands

```bash
make help                 # Show all available commands
make build               # Build the application
make run                 # Run the application
make dev                 # Run with live reload
make test                # Run tests
make test-coverage       # Run tests with coverage
make lint                # Lint code
make fmt                 # Format code
make docker-dev-up       # Start development services
make docker-dev-down     # Stop development services
make migrate-up          # Run database migrations
make migrate-down        # Rollback migrations
make migrate-create      # Create new migration
make setup               # Setup complete dev environment
```

### Database Migrations

Create a new migration:

```bash
make migrate-create
# Enter migration name when prompted
```

Run migrations:

```bash
make migrate-up
```

Rollback migrations:

```bash
make migrate-down
```

### Testing

Run tests:

```bash
make test
```

Run tests with coverage:

```bash
make test-coverage
```

### Code Quality

Format code:

```bash
make fmt
```

Lint code:

```bash
make lint
```

## Configuration

The application uses a hierarchical configuration system:

1. Default values (in code)
2. Configuration file (`config.yaml`)
3. Environment variables (with `APP_` prefix)

Environment variables override file configuration. Use dot notation with underscores:
* `APP_SERVER_PORT` overrides `server.port`
* `APP_DATABASE_HOST` overrides `database.host`

## Logging

The application uses structured logging with the following features:

* Request ID tracing
* Contextual information (user_id, component, etc.)
* Different log levels (debug, info, warn, error, fatal)
* JSON format for production, console format for development
* HTTP request/response logging
* Business event logging
* Security event logging

## Error Handling

The application implements standardized error handling:

* Custom error types with codes and HTTP status mapping
* Validation error handling with field-specific messages
* Database error handling with appropriate user messages
* Global error handler for unhandled errors
* Structured error responses

## Security Features

* JWT-based authentication with access and refresh tokens
* Password hashing with bcrypt
* Refresh token rotation and revocation
* HTTP-only cookies for refresh tokens
* Request ID for audit trails
* Input validation and sanitization
* CORS configuration
* Rate limiting (configurable)

## Contributing

1. Follow the established code structure and conventions
2. Write tests for new functionality
3. Ensure all tests pass and code is properly formatted
4. Update documentation as needed

## License

[Add your license information here]
