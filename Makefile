# Pantry Pal Backend Makefile

# Variables
APP_NAME=pantry-pal
BINARY_NAME=pantry-pal-api
DOCKER_IMAGE=pantry-pal:latest
MIGRATION_PATH=internal/infra/persistence/migrations
DATABASE_URL=postgres://pantrypal:pantrypal_dev_password@localhost:5432/pantrypal?sslmode=disable

# Go related variables
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# Build the application
.PHONY: build
build:
	$(GOBUILD) -o bin/$(BINARY_NAME) cmd/api/main.go

# Run the application
.PHONY: run
run:
	$(GOCMD) run cmd/api/main.go

# Run with live reload (requires air: go install github.com/cosmtrek/air@latest)
.PHONY: dev
dev:
	air

# Clean build artifacts
.PHONY: clean
clean:
	$(GOCLEAN)
	rm -f bin/$(BINARY_NAME)

# Run tests
.PHONY: test
test:
	$(GOTEST) -v ./...

# Run tests with coverage
.PHONY: test-coverage
test-coverage:
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html

# Download dependencies
.PHONY: deps
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# Format code
.PHONY: fmt
fmt:
	$(GOCMD) fmt ./...

# Lint code (requires golangci-lint)
.PHONY: lint
lint:
	golangci-lint run

# Start development environment (PostgreSQL + Redis)
.PHONY: docker-dev-up
docker-dev-up:
	docker-compose up -d postgres redis

# Stop development environment
.PHONY: docker-dev-down
docker-dev-down:
	docker-compose down

# Start all services including the app
.PHONY: docker-up
docker-up:
	docker-compose up -d

# Stop all services
.PHONY: docker-down
docker-down:
	docker-compose down

# View logs
.PHONY: docker-logs
docker-logs:
	docker-compose logs -f

# Database migrations (requires migrate CLI tool)
.PHONY: migrate-up
migrate-up:
	migrate -path $(MIGRATION_PATH) -database "$(DATABASE_URL)" up

.PHONY: migrate-down
migrate-down:
	migrate -path $(MIGRATION_PATH) -database "$(DATABASE_URL)" down

.PHONY: migrate-force
migrate-force:
	migrate -path $(MIGRATION_PATH) -database "$(DATABASE_URL)" force $(VERSION)

.PHONY: migrate-version
migrate-version:
	migrate -path $(MIGRATION_PATH) -database "$(DATABASE_URL)" version

# Create a new migration file
.PHONY: migrate-create
migrate-create:
	@read -p "Enter migration name: " name; \
	migrate create -ext sql -dir $(MIGRATION_PATH) -seq $$name

# Database operations
.PHONY: db-reset
db-reset: migrate-down migrate-up

# Install development tools
.PHONY: install-tools
install-tools:
	go install github.com/cosmtrek/air@latest
	go install github.com/golang-migrate/migrate/v4/cmd/migrate@latest
	go install github.com/golangci-lint/golangci-lint/cmd/golangci-lint@latest

# Setup development environment
.PHONY: setup
setup: install-tools deps docker-dev-up
	@echo "Waiting for database to be ready..."
	@sleep 5
	@make migrate-up
	@echo "Development environment is ready!"

# Health check
.PHONY: health
health:
	curl -f http://localhost:8080/health || exit 1

# Build Docker image
.PHONY: docker-build
docker-build:
	docker build -t $(DOCKER_IMAGE) .

# Help
.PHONY: help
help:
	@echo "Available commands:"
	@echo "  build          - Build the application binary"
	@echo "  run            - Run the application"
	@echo "  dev            - Run with live reload (requires air)"
	@echo "  clean          - Clean build artifacts"
	@echo "  test           - Run tests"
	@echo "  test-coverage  - Run tests with coverage report"
	@echo "  deps           - Download and tidy dependencies"
	@echo "  fmt            - Format code"
	@echo "  lint           - Lint code"
	@echo "  docker-dev-up  - Start development services (DB + Redis)"
	@echo "  docker-dev-down- Stop development services"
	@echo "  docker-up      - Start all services"
	@echo "  docker-down    - Stop all services"
	@echo "  docker-logs    - View service logs"
	@echo "  migrate-up     - Run database migrations"
	@echo "  migrate-down   - Rollback database migrations"
	@echo "  migrate-create - Create new migration file"
	@echo "  db-reset       - Reset database (down + up)"
	@echo "  setup          - Setup complete development environment"
	@echo "  health         - Check application health"
	@echo "  help           - Show this help message"
