app:
  name: "Pantry Pal"
  version: "1.0.0"
  environment: "development"

server:
  host: "0.0.0.0"
  port: "8080"
  read_timeout: "10s"
  write_timeout: "10s"
  idle_timeout: "120s"
  body_limit: "4MB"
  prefork: false
  
  cors:
    allow_origins:
      - "http://localhost:3000"
      - "http://localhost:3001"
    allow_methods:
      - "GET"
      - "POST"
      - "PUT"
      - "PATCH"
      - "DELETE"
      - "OPTIONS"
    allow_headers:
      - "*"
    allow_credentials: true
    max_age: 86400
  
  rate_limit:
    enabled: true
    max: 100
    expiration: "1m"

database:
  host: "localhost"
  port: "5432"
  username: "pantrypal"
  database: "pantrypal"
  ssl_mode: "disable"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: "1h"
  log_level: "warn"

redis:
  host: "localhost"
  port: "6379"
  database: 0
  pool_size: 10
  min_idle_conns: 5
  max_retries: 3

auth:
  jwt:
    access_token_expiration: "15m"
    refresh_token_expiration: "7d"
    issuer: "pantry-pal"

logger:
  level: "info"
  format: "console"  # Use "json" for production
  time_format: "2006-01-02T15:04:05Z07:00"
