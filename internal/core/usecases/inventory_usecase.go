package usecases

import (
	"context"
	"time"

	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// InventoryUsecase handles inventory-related business logic
type InventoryUsecase struct {
	inventoryRepo      domain.InventoryItemRepository
	pantryRepo         domain.PantryRepository
	productVariantRepo domain.ProductVariantRepository
	unitRepo           domain.UnitOfMeasureRepository
	locationRepo       domain.PantryLocationRepository
	authzService       domain.PantryAuthorizationService
	eventDispatcher    EventDispatcher
	logger             Logger
}

// NewInventoryUsecase creates a new inventory use case
func NewInventoryUsecase(
	inventoryRepo domain.InventoryItemRepository,
	pantryRepo domain.PantryRepository,
	productVariantRepo domain.ProductVariantRepository,
	unitRepo domain.UnitOfMeasureRepository,
	locationRepo domain.PantryLocationRepository,
	authzService domain.PantryAuthorizationService,
	eventDispatcher EventDispatcher,
	logger Logger,
) *InventoryUsecase {
	return &InventoryUsecase{
		inventoryRepo:      inventoryRepo,
		pantryRepo:         pantryRepo,
		productVariantRepo: productVariantRepo,
		unitRepo:           unitRepo,
		locationRepo:       locationRepo,
		authzService:       authzService,
		eventDispatcher:    eventDispatcher,
		logger:             logger,
	}
}

// CreateInventoryItem creates a new inventory item
func (uc *InventoryUsecase) CreateInventoryItem(ctx context.Context, userID, pantryID uuid.UUID, req *domain.CreateInventoryItemRequest) (*domain.InventoryItem, error) {
	// Check if user can add items to pantry
	canAdd, err := uc.authzService.CheckPermission(userID, pantryID, domain.PermissionCreateItems)
	if err != nil {
		uc.logger.Error("Failed to check add items permission", err, map[string]interface{}{
			"user_id":   userID,
			"pantry_id": pantryID,
		})
		return nil, errors.ErrInternalError
	}

	if !canAdd {
		return nil, errors.ErrForbidden
	}

	// Validate pantry exists
	_, err = uc.pantryRepo.GetByID(pantryID)
	if err != nil {
		return nil, err
	}

	// Validate product variant exists
	_, err = uc.productVariantRepo.GetByID(req.ProductVariantID)
	if err != nil {
		return nil, err
	}

	// Validate unit of measure exists
	_, err = uc.unitRepo.GetByID(req.UnitOfMeasureID)
	if err != nil {
		return nil, err
	}

	// Validate location if provided
	if req.LocationID != nil {
		location, err := uc.locationRepo.GetByID(*req.LocationID)
		if err != nil {
			return nil, err
		}
		// Ensure location belongs to the same pantry
		if location.PantryID != pantryID {
			return nil, errors.New(errors.ErrCodeInvalidInput, "Location does not belong to the specified pantry")
		}
	}

	// Validate request data
	if err := domain.ValidateInventoryQuantity(req.Quantity); err != nil {
		return nil, err
	}

	if err := domain.ValidateInventoryPrice(req.PurchasePrice); err != nil {
		return nil, err
	}

	if err := domain.ValidateInventoryNotes(req.Notes); err != nil {
		return nil, err
	}

	// Parse dates
	purchaseDate, err := parseDate(req.PurchaseDate)
	if err != nil {
		return nil, err
	}

	expirationDate, err := parseDate(req.ExpirationDate)
	if err != nil {
		return nil, err
	}

	// Create inventory item
	item := domain.NewInventoryItem(
		pantryID,
		req.LocationID,
		req.ProductVariantID,
		req.Quantity,
		req.UnitOfMeasureID,
		purchaseDate,
		expirationDate,
		req.PurchasePrice,
		req.Notes,
	)

	// Save to repository
	if err := uc.inventoryRepo.Create(item); err != nil {
		uc.logger.Error("Failed to create inventory item", err, map[string]interface{}{
			"user_id":            userID,
			"pantry_id":          pantryID,
			"product_variant_id": req.ProductVariantID,
		})
		return nil, err
	}

	// Dispatch domain events
	uc.eventDispatcher.DispatchEvents(ctx, item.GetEvents())
	item.ClearEvents()

	uc.logger.Info("Inventory item created successfully", map[string]interface{}{
		"user_id":   userID,
		"item_id":   item.ID,
		"pantry_id": item.PantryID,
		"quantity":  item.Quantity,
	})

	return item, nil
}

// GetInventoryItem retrieves an inventory item by ID
func (uc *InventoryUsecase) GetInventoryItem(ctx context.Context, userID, itemID uuid.UUID) (*domain.InventoryItem, error) {
	// Get the item first to check pantry access
	item, err := uc.inventoryRepo.GetByID(itemID)
	if err != nil {
		return nil, err
	}

	// Check if user can view items in this pantry
	canView, err := uc.authzService.CheckPermission(userID, item.PantryID, domain.PermissionViewItems)
	if err != nil {
		uc.logger.Error("Failed to check view items permission", err, map[string]interface{}{
			"user_id":   userID,
			"item_id":   itemID,
			"pantry_id": item.PantryID,
		})
		return nil, errors.ErrInternalError
	}

	if !canView {
		return nil, errors.ErrForbidden
	}

	return item, nil
}

// GetPantryInventory retrieves inventory items for a pantry with pagination
func (uc *InventoryUsecase) GetPantryInventory(ctx context.Context, userID, pantryID uuid.UUID, page, limit int) ([]*domain.InventoryItem, int64, error) {
	// Check if user can view items in this pantry
	canView, err := uc.authzService.CheckPermission(userID, pantryID, domain.PermissionViewItems)
	if err != nil {
		uc.logger.Error("Failed to check view items permission", err, map[string]interface{}{
			"user_id":   userID,
			"pantry_id": pantryID,
		})
		return nil, 0, errors.ErrInternalError
	}

	if !canView {
		return nil, 0, errors.ErrForbidden
	}

	// Get inventory items
	items, total, err := uc.inventoryRepo.GetByPantryID(pantryID, page, limit)
	if err != nil {
		uc.logger.Error("Failed to get pantry inventory", err, map[string]interface{}{
			"user_id":   userID,
			"pantry_id": pantryID,
		})
		return nil, 0, err
	}

	return items, total, nil
}

// UpdateInventoryItem updates an existing inventory item
func (uc *InventoryUsecase) UpdateInventoryItem(ctx context.Context, userID, itemID uuid.UUID, req *domain.UpdateInventoryItemRequest) (*domain.InventoryItem, error) {
	// Get the item first to check pantry access
	item, err := uc.inventoryRepo.GetByID(itemID)
	if err != nil {
		return nil, err
	}

	// Check if user can edit items in this pantry
	canEdit, err := uc.authzService.CheckPermission(userID, item.PantryID, domain.PermissionEditItems)
	if err != nil {
		uc.logger.Error("Failed to check edit items permission", err, map[string]interface{}{
			"user_id":   userID,
			"item_id":   itemID,
			"pantry_id": item.PantryID,
		})
		return nil, errors.ErrInternalError
	}

	if !canEdit {
		return nil, errors.ErrForbidden
	}

	// Validate location if provided
	if req.LocationID != nil {
		location, err := uc.locationRepo.GetByID(*req.LocationID)
		if err != nil {
			return nil, err
		}
		// Ensure location belongs to the same pantry
		if location.PantryID != item.PantryID {
			return nil, errors.New(errors.ErrCodeInvalidInput, "Location does not belong to the specified pantry")
		}
	}

	// Validate request data
	if err := domain.ValidateInventoryQuantity(req.Quantity); err != nil {
		return nil, err
	}

	if err := domain.ValidateInventoryPrice(req.PurchasePrice); err != nil {
		return nil, err
	}

	if err := domain.ValidateInventoryNotes(req.Notes); err != nil {
		return nil, err
	}

	// Parse dates
	purchaseDate, err := parseDate(req.PurchaseDate)
	if err != nil {
		return nil, err
	}

	expirationDate, err := parseDate(req.ExpirationDate)
	if err != nil {
		return nil, err
	}

	// Update item details
	if err := item.UpdateDetails(
		req.Quantity,
		req.LocationID,
		purchaseDate,
		expirationDate,
		req.PurchasePrice,
		req.Notes,
	); err != nil {
		return nil, err
	}

	// Save to repository
	if err := uc.inventoryRepo.Update(item); err != nil {
		uc.logger.Error("Failed to update inventory item", err, map[string]interface{}{
			"user_id": userID,
			"item_id": itemID,
		})
		return nil, err
	}

	// Dispatch domain events
	uc.eventDispatcher.DispatchEvents(ctx, item.GetEvents())
	item.ClearEvents()

	uc.logger.Info("Inventory item updated successfully", map[string]interface{}{
		"user_id": userID,
		"item_id": itemID,
	})

	return item, nil
}

// ConsumeInventoryItem consumes a quantity from an inventory item
func (uc *InventoryUsecase) ConsumeInventoryItem(ctx context.Context, userID, itemID uuid.UUID, req *domain.ConsumeInventoryRequest) (*domain.InventoryItem, error) {
	// Get the item first to check pantry access
	item, err := uc.inventoryRepo.GetByID(itemID)
	if err != nil {
		return nil, err
	}

	// Check if user can edit items in this pantry
	canEdit, err := uc.authzService.CheckPermission(userID, item.PantryID, domain.PermissionEditItems)
	if err != nil {
		uc.logger.Error("Failed to check edit items permission", err, map[string]interface{}{
			"user_id":   userID,
			"item_id":   itemID,
			"pantry_id": item.PantryID,
		})
		return nil, errors.ErrInternalError
	}

	if !canEdit {
		return nil, errors.ErrForbidden
	}

	// Consume quantity
	if err := item.ConsumeQuantity(req.ConsumedQuantity); err != nil {
		return nil, err
	}

	// Save to repository
	if err := uc.inventoryRepo.Update(item); err != nil {
		uc.logger.Error("Failed to update inventory item after consumption", err, map[string]interface{}{
			"user_id":           userID,
			"item_id":           itemID,
			"consumed_quantity": req.ConsumedQuantity,
		})
		return nil, err
	}

	// Dispatch domain events
	uc.eventDispatcher.DispatchEvents(ctx, item.GetEvents())
	item.ClearEvents()

	uc.logger.Info("Inventory item consumed successfully", map[string]interface{}{
		"user_id":           userID,
		"item_id":           itemID,
		"consumed_quantity": req.ConsumedQuantity,
		"remaining":         item.Quantity,
	})

	return item, nil
}

// BulkCreateInventoryItems creates multiple inventory items in a single transaction
func (uc *InventoryUsecase) BulkCreateInventoryItems(ctx context.Context, userID, pantryID uuid.UUID, req *domain.BulkCreateInventoryItemsRequest) (*domain.BulkCreateInventoryItemsResponse, error) {
	// Check if user can add items to pantry
	canAdd, err := uc.authzService.CheckPermission(userID, pantryID, domain.PermissionCreateItems)
	if err != nil {
		uc.logger.Error("Failed to check add items permission for bulk create", err, map[string]interface{}{
			"user_id":   userID,
			"pantry_id": pantryID,
		})
		return nil, errors.ErrInternalError
	}

	if !canAdd {
		return nil, errors.ErrForbidden
	}

	// Validate pantry exists
	_, err = uc.pantryRepo.GetByID(pantryID)
	if err != nil {
		return nil, err
	}

	response := &domain.BulkCreateInventoryItemsResponse{
		TotalCount: len(req.Items),
		Results:    make([]domain.BulkInventoryItemOperationResult, len(req.Items)),
		Summary: domain.BulkOperationSummary{
			TotalItems:       len(req.Items),
			AffectedPantries: []uuid.UUID{pantryID},
		},
	}

	// Process each item
	for i, itemReq := range req.Items {
		result := domain.BulkInventoryItemOperationResult{
			Index: i,
		}

		// Create individual item
		item, err := uc.createSingleInventoryItem(ctx, userID, pantryID, &itemReq)
		if err != nil {
			result.Success = false
			result.Error = &domain.BulkOperationError{
				Code:    uc.mapErrorToBulkCode(err),
				Message: err.Error(),
			}
			response.FailureCount++
		} else {
			result.Success = true
			result.ItemID = &item.ID
			result.Item = uc.convertToResponse(item)
			response.SuccessCount++
			response.Summary.TotalQuantityAdded += item.Quantity
		}

		response.Results[i] = result
	}

	// Calculate success rate
	if response.TotalCount > 0 {
		response.Summary.SuccessfulItems = response.SuccessCount
		response.Summary.FailedItems = response.FailureCount
		response.Summary.SuccessRate = float64(response.SuccessCount) / float64(response.TotalCount) * 100
	}

	uc.logger.Info("Bulk create inventory items completed", map[string]interface{}{
		"user_id":       userID,
		"pantry_id":     pantryID,
		"total_items":   response.TotalCount,
		"success_count": response.SuccessCount,
		"failure_count": response.FailureCount,
		"success_rate":  response.Summary.SuccessRate,
	})

	return response, nil
}

// BulkUpdateInventoryItems updates multiple inventory items
func (uc *InventoryUsecase) BulkUpdateInventoryItems(ctx context.Context, userID uuid.UUID, req *domain.BulkUpdateInventoryItemsRequest) (*domain.BulkUpdateInventoryItemsResponse, error) {
	response := &domain.BulkUpdateInventoryItemsResponse{
		TotalCount: len(req.Updates),
		Results:    make([]domain.BulkInventoryItemOperationResult, len(req.Updates)),
		Summary: domain.BulkOperationSummary{
			TotalItems: len(req.Updates),
		},
	}

	affectedPantries := make(map[uuid.UUID]bool)

	// Process each update
	for i, updateReq := range req.Updates {
		result := domain.BulkInventoryItemOperationResult{
			Index:  i,
			ItemID: &updateReq.ItemID,
		}

		// Update individual item
		item, err := uc.UpdateInventoryItem(ctx, userID, updateReq.ItemID, &updateReq.Updates)
		if err != nil {
			result.Success = false
			result.Error = &domain.BulkOperationError{
				Code:    uc.mapErrorToBulkCode(err),
				Message: err.Error(),
			}
			response.FailureCount++
		} else {
			result.Success = true
			result.Item = uc.convertToResponse(item)
			response.SuccessCount++
			affectedPantries[item.PantryID] = true
		}

		response.Results[i] = result
	}

	// Set affected pantries
	for pantryID := range affectedPantries {
		response.Summary.AffectedPantries = append(response.Summary.AffectedPantries, pantryID)
	}

	// Calculate success rate
	if response.TotalCount > 0 {
		response.Summary.SuccessfulItems = response.SuccessCount
		response.Summary.FailedItems = response.FailureCount
		response.Summary.SuccessRate = float64(response.SuccessCount) / float64(response.TotalCount) * 100
	}

	uc.logger.Info("Bulk update inventory items completed", map[string]interface{}{
		"user_id":       userID,
		"total_items":   response.TotalCount,
		"success_count": response.SuccessCount,
		"failure_count": response.FailureCount,
		"success_rate":  response.Summary.SuccessRate,
	})

	return response, nil
}

// BulkConsumeInventoryItems consumes quantities from multiple inventory items
func (uc *InventoryUsecase) BulkConsumeInventoryItems(ctx context.Context, userID uuid.UUID, req *domain.BulkConsumeInventoryRequest) (*domain.BulkConsumeInventoryResponse, error) {
	response := &domain.BulkConsumeInventoryResponse{
		TotalCount: len(req.Consumptions),
		Results:    make([]domain.BulkInventoryItemOperationResult, len(req.Consumptions)),
		Summary: domain.BulkOperationSummary{
			TotalItems: len(req.Consumptions),
		},
	}

	affectedPantries := make(map[uuid.UUID]bool)

	// Process each consumption
	for i, consumeReq := range req.Consumptions {
		result := domain.BulkInventoryItemOperationResult{
			Index:  i,
			ItemID: &consumeReq.ItemID,
		}

		// Create consume request
		var notes *string
		if consumeReq.Notes != "" {
			notes = &consumeReq.Notes
		}
		consumeRequest := &domain.ConsumeInventoryRequest{
			ConsumedQuantity: consumeReq.ConsumedQuantity,
			Notes:            notes,
		}

		// Consume from individual item
		item, err := uc.ConsumeInventoryItem(ctx, userID, consumeReq.ItemID, consumeRequest)
		if err != nil {
			result.Success = false
			result.Error = &domain.BulkOperationError{
				Code:    uc.mapErrorToBulkCode(err),
				Message: err.Error(),
			}
			response.FailureCount++
		} else {
			result.Success = true
			result.Item = uc.convertToResponse(item)
			response.SuccessCount++
			response.Summary.TotalQuantityConsumed += consumeReq.ConsumedQuantity
			affectedPantries[item.PantryID] = true
		}

		response.Results[i] = result
	}

	// Set affected pantries
	for pantryID := range affectedPantries {
		response.Summary.AffectedPantries = append(response.Summary.AffectedPantries, pantryID)
	}

	// Calculate success rate
	if response.TotalCount > 0 {
		response.Summary.SuccessfulItems = response.SuccessCount
		response.Summary.FailedItems = response.FailureCount
		response.Summary.SuccessRate = float64(response.SuccessCount) / float64(response.TotalCount) * 100
	}

	uc.logger.Info("Bulk consume inventory items completed", map[string]interface{}{
		"user_id":        userID,
		"total_items":    response.TotalCount,
		"success_count":  response.SuccessCount,
		"failure_count":  response.FailureCount,
		"success_rate":   response.Summary.SuccessRate,
		"total_consumed": response.Summary.TotalQuantityConsumed,
	})

	return response, nil
}

// BulkDeleteInventoryItems deletes multiple inventory items
func (uc *InventoryUsecase) BulkDeleteInventoryItems(ctx context.Context, userID uuid.UUID, req *domain.BulkDeleteInventoryItemsRequest) (*domain.BulkDeleteInventoryItemsResponse, error) {
	response := &domain.BulkDeleteInventoryItemsResponse{
		TotalCount: len(req.ItemIDs),
		Results:    make([]domain.BulkInventoryItemOperationResult, len(req.ItemIDs)),
		Summary: domain.BulkOperationSummary{
			TotalItems: len(req.ItemIDs),
		},
	}

	affectedPantries := make(map[uuid.UUID]bool)

	// Process each deletion
	for i, itemID := range req.ItemIDs {
		result := domain.BulkInventoryItemOperationResult{
			Index:  i,
			ItemID: &itemID,
		}

		// Get the item first to check pantry access
		item, err := uc.inventoryRepo.GetByID(itemID)
		if err != nil {
			result.Success = false
			result.Error = &domain.BulkOperationError{
				Code:    uc.mapErrorToBulkCode(err),
				Message: err.Error(),
			}
			response.FailureCount++
			response.Results[i] = result
			continue
		}

		// Check if user can delete items in this pantry
		canDelete, err := uc.authzService.CheckPermission(userID, item.PantryID, domain.PermissionDeleteItems)
		if err != nil {
			result.Success = false
			result.Error = &domain.BulkOperationError{
				Code:    "PERMISSION_CHECK_FAILED",
				Message: "Failed to check delete permission",
			}
			response.FailureCount++
			response.Results[i] = result
			continue
		}

		if !canDelete {
			result.Success = false
			result.Error = &domain.BulkOperationError{
				Code:    "FORBIDDEN",
				Message: "Insufficient permissions to delete item",
			}
			response.FailureCount++
			response.Results[i] = result
			continue
		}

		// Soft delete the item
		item.SoftDelete()

		// Save to repository
		if err := uc.inventoryRepo.Update(item); err != nil {
			result.Success = false
			result.Error = &domain.BulkOperationError{
				Code:    "UPDATE_FAILED",
				Message: "Failed to save deletion",
			}
			response.FailureCount++
		} else {
			result.Success = true
			response.SuccessCount++
			affectedPantries[item.PantryID] = true

			// Dispatch domain events
			uc.eventDispatcher.DispatchEvents(ctx, item.GetEvents())
			item.ClearEvents()
		}

		response.Results[i] = result
	}

	// Set affected pantries
	for pantryID := range affectedPantries {
		response.Summary.AffectedPantries = append(response.Summary.AffectedPantries, pantryID)
	}

	// Calculate success rate
	if response.TotalCount > 0 {
		response.Summary.SuccessfulItems = response.SuccessCount
		response.Summary.FailedItems = response.FailureCount
		response.Summary.SuccessRate = float64(response.SuccessCount) / float64(response.TotalCount) * 100
	}

	uc.logger.Info("Bulk delete inventory items completed", map[string]interface{}{
		"user_id":       userID,
		"total_items":   response.TotalCount,
		"success_count": response.SuccessCount,
		"failure_count": response.FailureCount,
		"success_rate":  response.Summary.SuccessRate,
	})

	return response, nil
}

// ConsumeRecipeIngredients consumes ingredients for a recipe with smart selection
func (uc *InventoryUsecase) ConsumeRecipeIngredients(ctx context.Context, userID, pantryID uuid.UUID, req *domain.RecipeConsumptionRequest) (*domain.RecipeConsumptionResponse, error) {
	// Check if user can edit items in this pantry
	canEdit, err := uc.authzService.CheckPermission(userID, pantryID, domain.PermissionEditItems)
	if err != nil {
		uc.logger.Error("Failed to check edit items permission for recipe consumption", err, map[string]interface{}{
			"user_id":   userID,
			"pantry_id": pantryID,
		})
		return nil, errors.ErrInternalError
	}

	if !canEdit {
		return nil, errors.ErrForbidden
	}

	// Validate pantry exists
	_, err = uc.pantryRepo.GetByID(pantryID)
	if err != nil {
		return nil, err
	}

	response := &domain.RecipeConsumptionResponse{
		RecipeID:         req.RecipeID,
		RecipeName:       req.RecipeName,
		TotalIngredients: len(req.Ingredients),
		Results:          make([]domain.RecipeIngredientConsumptionResult, len(req.Ingredients)),
		Summary: domain.RecipeConsumptionSummary{
			TotalIngredients: len(req.Ingredients),
		},
	}

	// Process each ingredient
	for i, ingredient := range req.Ingredients {
		result := uc.processRecipeIngredient(ctx, userID, pantryID, &ingredient, req.AutoSelect)
		response.Results[i] = result

		// Update counters
		if result.Success {
			response.SuccessCount++
			response.Summary.SuccessfulIngredients++
		} else if result.Partial {
			response.PartialCount++
			response.Summary.PartialIngredients++
		} else {
			response.FailureCount++
			response.Summary.FailedIngredients++
		}

		// Update totals
		response.Summary.TotalItemsConsumed += len(result.ItemsConsumed)
		for _, item := range result.ItemsConsumed {
			if item.PurchasePrice != nil {
				response.Summary.TotalValue += *item.PurchasePrice * item.ConsumedQuantity
			}
		}
	}

	// Calculate completion rate
	if response.TotalIngredients > 0 {
		response.Summary.CompletionRate = float64(response.SuccessCount+response.PartialCount) / float64(response.TotalIngredients) * 100
	}

	// Determine if recipe can proceed
	response.CanProceed = response.SuccessCount > 0 || (req.AllowPartial && response.PartialCount > 0)
	response.Summary.CanProceedWithRecipe = response.CanProceed

	// Log business event
	uc.logger.Info("Recipe ingredients consumption completed", map[string]interface{}{
		"user_id":           userID,
		"pantry_id":         pantryID,
		"recipe_id":         req.RecipeID,
		"recipe_name":       req.RecipeName,
		"total_ingredients": response.TotalIngredients,
		"success_count":     response.SuccessCount,
		"partial_count":     response.PartialCount,
		"failure_count":     response.FailureCount,
		"completion_rate":   response.Summary.CompletionRate,
		"can_proceed":       response.CanProceed,
		"total_value":       response.Summary.TotalValue,
	})

	return response, nil
}

// processRecipeIngredient processes consumption of a single recipe ingredient
func (uc *InventoryUsecase) processRecipeIngredient(ctx context.Context, userID, pantryID uuid.UUID, ingredient *domain.RecipeIngredientConsumption, autoSelect bool) domain.RecipeIngredientConsumptionResult {
	result := domain.RecipeIngredientConsumptionResult{
		ProductVariantID: ingredient.ProductVariantID,
		RequiredQuantity: ingredient.RequiredQuantity,
		UnitOfMeasureID:  ingredient.UnitOfMeasureID,
		ItemsConsumed:    make([]domain.ConsumedInventoryItemDetail, 0),
	}

	// Get available inventory items for this product variant
	availableItems, err := uc.getAvailableItemsForIngredient(pantryID, ingredient, autoSelect)
	if err != nil {
		result.Error = &domain.BulkOperationError{
			Code:    uc.mapErrorToBulkCode(err),
			Message: err.Error(),
		}
		return result
	}

	if len(availableItems) == 0 {
		result.Error = &domain.BulkOperationError{
			Code:    "INSUFFICIENT_STOCK",
			Message: "No available inventory items for this ingredient",
		}
		return result
	}

	// Calculate total available quantity (with unit conversion)
	totalAvailable := 0.0
	for _, item := range availableItems {
		convertedQuantity, err := uc.convertQuantity(item.Quantity, item.UnitOfMeasureID, ingredient.UnitOfMeasureID)
		if err != nil {
			uc.logger.Error("Failed to convert units for ingredient", err, map[string]interface{}{
				"item_id":         item.ID,
				"from_unit":       item.UnitOfMeasureID,
				"to_unit":         ingredient.UnitOfMeasureID,
				"product_variant": ingredient.ProductVariantID,
			})
			continue // Skip items with conversion errors
		}
		totalAvailable += convertedQuantity
	}

	// Determine consumption strategy
	remainingToConsume := ingredient.RequiredQuantity

	if totalAvailable >= remainingToConsume {
		// Full consumption possible
		result.Success = true
		result.ConsumedQuantity = remainingToConsume
	} else if ingredient.AllowPartial && totalAvailable > 0 {
		// Partial consumption
		result.Partial = true
		result.ConsumedQuantity = totalAvailable
		remainingToConsume = totalAvailable
	} else {
		// Insufficient stock and partial not allowed
		result.Error = &domain.BulkOperationError{
			Code:    "INSUFFICIENT_STOCK",
			Message: "Insufficient stock for ingredient and partial consumption not allowed",
		}
		return result
	}

	result.RemainingQuantity = ingredient.RequiredQuantity - result.ConsumedQuantity

	// Consume from items
	for _, item := range availableItems {
		if remainingToConsume <= 0 {
			break
		}

		// Convert item quantity to recipe unit
		itemQuantityInRecipeUnit, err := uc.convertQuantity(item.Quantity, item.UnitOfMeasureID, ingredient.UnitOfMeasureID)
		if err != nil {
			continue // Skip items with conversion errors
		}

		// Calculate how much to consume from this item
		consumeFromItem := remainingToConsume
		if itemQuantityInRecipeUnit < consumeFromItem {
			consumeFromItem = itemQuantityInRecipeUnit
		}

		// Convert back to item's unit for actual consumption
		consumeInItemUnit, err := uc.convertQuantity(consumeFromItem, ingredient.UnitOfMeasureID, item.UnitOfMeasureID)
		if err != nil {
			continue // Skip items with conversion errors
		}

		// Consume from the item
		consumeRequest := &domain.ConsumeInventoryRequest{
			ConsumedQuantity: consumeInItemUnit,
			Notes:            &ingredient.Notes,
		}

		updatedItem, err := uc.ConsumeInventoryItem(ctx, userID, item.ID, consumeRequest)
		if err != nil {
			uc.logger.Error("Failed to consume from inventory item during recipe consumption", err, map[string]interface{}{
				"item_id":          item.ID,
				"consume_quantity": consumeInItemUnit,
				"product_variant":  ingredient.ProductVariantID,
			})
			continue // Skip failed consumptions
		}

		// Record the consumption
		consumedDetail := domain.ConsumedInventoryItemDetail{
			ItemID:            item.ID,
			ConsumedQuantity:  consumeFromItem, // In recipe unit
			RemainingQuantity: updatedItem.Quantity,
			LocationID:        item.LocationID,
			PurchasePrice:     item.PurchasePrice,
		}

		if item.ExpirationDate != nil {
			dateStr := item.ExpirationDate.Format("2006-01-02")
			consumedDetail.ExpirationDate = &dateStr
		}

		result.ItemsConsumed = append(result.ItemsConsumed, consumedDetail)
		remainingToConsume -= consumeFromItem
	}

	return result
}

// Helper functions for bulk operations

// createSingleInventoryItem creates a single inventory item (used by bulk operations)
func (uc *InventoryUsecase) createSingleInventoryItem(ctx context.Context, userID, pantryID uuid.UUID, req *domain.CreateInventoryItemRequest) (*domain.InventoryItem, error) {
	// Validate product variant exists
	_, err := uc.productVariantRepo.GetByID(req.ProductVariantID)
	if err != nil {
		return nil, err
	}

	// Validate unit of measure exists
	_, err = uc.unitRepo.GetByID(req.UnitOfMeasureID)
	if err != nil {
		return nil, err
	}

	// Validate location if provided
	if req.LocationID != nil {
		location, err := uc.locationRepo.GetByID(*req.LocationID)
		if err != nil {
			return nil, err
		}
		// Ensure location belongs to the same pantry
		if location.PantryID != pantryID {
			return nil, errors.New(errors.ErrCodeInvalidInput, "Location does not belong to the specified pantry")
		}
	}

	// Validate request data
	if err := domain.ValidateInventoryQuantity(req.Quantity); err != nil {
		return nil, err
	}

	if err := domain.ValidateInventoryPrice(req.PurchasePrice); err != nil {
		return nil, err
	}

	if err := domain.ValidateInventoryNotes(req.Notes); err != nil {
		return nil, err
	}

	// Parse dates
	purchaseDate, err := parseDate(req.PurchaseDate)
	if err != nil {
		return nil, err
	}

	expirationDate, err := parseDate(req.ExpirationDate)
	if err != nil {
		return nil, err
	}

	// Create inventory item
	item := domain.NewInventoryItem(
		pantryID,
		req.LocationID,
		req.ProductVariantID,
		req.Quantity,
		req.UnitOfMeasureID,
		purchaseDate,
		expirationDate,
		req.PurchasePrice,
		req.Notes,
	)

	// Save to repository
	if err := uc.inventoryRepo.Create(item); err != nil {
		return nil, err
	}

	// Dispatch domain events
	uc.eventDispatcher.DispatchEvents(ctx, item.GetEvents())
	item.ClearEvents()

	return item, nil
}

// mapErrorToBulkCode maps errors to bulk operation error codes
func (uc *InventoryUsecase) mapErrorToBulkCode(err error) string {
	if appErr := errors.GetAppError(err); appErr != nil {
		switch appErr.Code {
		case errors.ErrCodeNotFound:
			return "NOT_FOUND"
		case errors.ErrCodeInvalidInput:
			return "INVALID_INPUT"
		case errors.ErrCodeForbidden:
			return "FORBIDDEN"
		case errors.ErrCodeConflict:
			return "CONFLICT"
		default:
			return "INTERNAL_ERROR"
		}
	}
	return "UNKNOWN_ERROR"
}

// convertToResponse converts a domain inventory item to response format
func (uc *InventoryUsecase) convertToResponse(item *domain.InventoryItem) *domain.InventoryItemResponse {
	response := &domain.InventoryItemResponse{
		ID:               item.ID,
		PantryID:         item.PantryID,
		LocationID:       item.LocationID,
		ProductVariantID: item.ProductVariantID,
		Quantity:         item.Quantity,
		UnitOfMeasureID:  item.UnitOfMeasureID,
		PurchasePrice:    item.PurchasePrice,
		Notes:            item.Notes,
		Status:           item.GetStatus(),
		CreatedAt:        item.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:        item.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}

	// Convert dates to string pointers
	if item.PurchaseDate != nil {
		dateStr := item.PurchaseDate.Format("2006-01-02")
		response.PurchaseDate = &dateStr
	}

	if item.ExpirationDate != nil {
		dateStr := item.ExpirationDate.Format("2006-01-02")
		response.ExpirationDate = &dateStr
	}

	return response
}

// Helper functions for date parsing

// parseDate parses a date string in YYYY-MM-DD format to time.Time
func parseDate(dateStr *string) (*time.Time, error) {
	if dateStr == nil || *dateStr == "" {
		return nil, nil
	}

	parsedTime, err := time.Parse("2006-01-02", *dateStr)
	if err != nil {
		return nil, errors.New(errors.ErrCodeInvalidInput, "Invalid date format. Expected YYYY-MM-DD")
	}

	return &parsedTime, nil
}

// getAvailableItemsForIngredient gets available inventory items for a recipe ingredient
func (uc *InventoryUsecase) getAvailableItemsForIngredient(pantryID uuid.UUID, ingredient *domain.RecipeIngredientConsumption, autoSelect bool) ([]*domain.InventoryItem, error) {
	// If preferred items are specified, try those first
	if len(ingredient.PreferredItems) > 0 {
		preferredItems := make([]*domain.InventoryItem, 0)
		for _, itemID := range ingredient.PreferredItems {
			item, err := uc.inventoryRepo.GetByID(itemID)
			if err != nil {
				continue // Skip items that don't exist
			}

			// Verify item belongs to the pantry and matches product variant
			if item.PantryID == pantryID && item.ProductVariantID == ingredient.ProductVariantID && item.Quantity > 0 {
				preferredItems = append(preferredItems, item)
			}
		}

		if len(preferredItems) > 0 {
			return uc.sortItemsForConsumption(preferredItems, autoSelect), nil
		}
	}

	// Get all items for this product variant in the pantry
	allItems, _, err := uc.inventoryRepo.GetByPantryID(pantryID, 1, 1000) // Get up to 1000 items
	if err != nil {
		return nil, err
	}

	// Filter items for this product variant with available quantity
	availableItems := make([]*domain.InventoryItem, 0)
	for _, item := range allItems {
		if item.ProductVariantID == ingredient.ProductVariantID && item.Quantity > 0 && item.DeletedAt == nil {
			availableItems = append(availableItems, item)
		}
	}

	return uc.sortItemsForConsumption(availableItems, autoSelect), nil
}

// sortItemsForConsumption sorts items for optimal consumption order
func (uc *InventoryUsecase) sortItemsForConsumption(items []*domain.InventoryItem, autoSelect bool) []*domain.InventoryItem {
	if !autoSelect {
		return items // Return as-is if no auto-selection
	}

	// Sort by expiration date (earliest first), then by creation date (oldest first)
	// This implements FIFO (First In, First Out) with expiration priority
	sortedItems := make([]*domain.InventoryItem, len(items))
	copy(sortedItems, items)

	// Simple bubble sort for demonstration (could be optimized with sort.Slice)
	for i := 0; i < len(sortedItems)-1; i++ {
		for j := 0; j < len(sortedItems)-i-1; j++ {
			shouldSwap := false

			item1 := sortedItems[j]
			item2 := sortedItems[j+1]

			// Priority 1: Items with expiration dates come first
			if item1.ExpirationDate == nil && item2.ExpirationDate != nil {
				shouldSwap = true
			} else if item1.ExpirationDate != nil && item2.ExpirationDate != nil {
				// Both have expiration dates, sort by earliest first
				if item1.ExpirationDate.After(*item2.ExpirationDate) {
					shouldSwap = true
				}
			} else if item1.ExpirationDate == nil && item2.ExpirationDate == nil {
				// Neither has expiration date, sort by creation date (FIFO)
				if item1.CreatedAt.After(item2.CreatedAt) {
					shouldSwap = true
				}
			}

			if shouldSwap {
				sortedItems[j], sortedItems[j+1] = sortedItems[j+1], sortedItems[j]
			}
		}
	}

	return sortedItems
}

// convertQuantity converts quantity between different units of measure
func (uc *InventoryUsecase) convertQuantity(quantity float64, fromUnitID, toUnitID uuid.UUID) (float64, error) {
	// If units are the same, no conversion needed
	if fromUnitID == toUnitID {
		return quantity, nil
	}

	// Get both units
	fromUnit, err := uc.unitRepo.GetByID(fromUnitID)
	if err != nil {
		return 0, err
	}

	toUnit, err := uc.unitRepo.GetByID(toUnitID)
	if err != nil {
		return 0, err
	}

	// Check if units are compatible (same type)
	if !fromUnit.IsCompatibleWith(toUnit) {
		return 0, errors.New(errors.ErrCodeInvalidInput, "Units are not compatible for conversion")
	}

	// Convert from source unit to base unit
	baseQuantity, err := fromUnit.ConvertToBaseUnit(quantity)
	if err != nil {
		return 0, err
	}

	// Convert from base unit to target unit
	targetQuantity, err := toUnit.ConvertFromBaseUnit(baseQuantity)
	if err != nil {
		return 0, err
	}

	return targetQuantity, nil
}
