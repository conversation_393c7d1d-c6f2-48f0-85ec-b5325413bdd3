package usecases

import (
	"context"
	"time"

	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// InventoryUsecase handles inventory-related business logic
type InventoryUsecase struct {
	inventoryRepo      domain.InventoryItemRepository
	pantryRepo         domain.PantryRepository
	productVariantRepo domain.ProductVariantRepository
	unitRepo           domain.UnitOfMeasureRepository
	locationRepo       domain.PantryLocationRepository
	authzService       domain.PantryAuthorizationService
	eventDispatcher    EventDispatcher
	logger             Logger
}

// NewInventoryUsecase creates a new inventory use case
func NewInventoryUsecase(
	inventoryRepo domain.InventoryItemRepository,
	pantryRepo domain.PantryRepository,
	productVariantRepo domain.ProductVariantRepository,
	unitRepo domain.UnitOfMeasureRepository,
	locationRepo domain.PantryLocationRepository,
	authzService domain.PantryAuthorizationService,
	eventDispatcher EventDispatcher,
	logger Logger,
) *InventoryUsecase {
	return &InventoryUsecase{
		inventoryRepo:      inventoryRepo,
		pantryRepo:         pantryRepo,
		productVariantRepo: productVariantRepo,
		unitRepo:           unitRepo,
		locationRepo:       locationRepo,
		authzService:       authzService,
		eventDispatcher:    eventDispatcher,
		logger:             logger,
	}
}

// CreateInventoryItem creates a new inventory item
func (uc *InventoryUsecase) CreateInventoryItem(ctx context.Context, userID, pantryID uuid.UUID, req *domain.CreateInventoryItemRequest) (*domain.InventoryItem, error) {
	// Check if user can add items to pantry
	canAdd, err := uc.authzService.CheckPermission(userID, pantryID, domain.PermissionCreateItems)
	if err != nil {
		uc.logger.Error("Failed to check add items permission", err, map[string]interface{}{
			"user_id":   userID,
			"pantry_id": pantryID,
		})
		return nil, errors.ErrInternalError
	}

	if !canAdd {
		return nil, errors.ErrForbidden
	}

	// Validate pantry exists
	_, err = uc.pantryRepo.GetByID(pantryID)
	if err != nil {
		return nil, err
	}

	// Validate product variant exists
	_, err = uc.productVariantRepo.GetByID(req.ProductVariantID)
	if err != nil {
		return nil, err
	}

	// Validate unit of measure exists
	_, err = uc.unitRepo.GetByID(req.UnitOfMeasureID)
	if err != nil {
		return nil, err
	}

	// Validate location if provided
	if req.LocationID != nil {
		location, err := uc.locationRepo.GetByID(*req.LocationID)
		if err != nil {
			return nil, err
		}
		// Ensure location belongs to the same pantry
		if location.PantryID != pantryID {
			return nil, errors.New(errors.ErrCodeInvalidInput, "Location does not belong to the specified pantry")
		}
	}

	// Validate request data
	if err := domain.ValidateInventoryQuantity(req.Quantity); err != nil {
		return nil, err
	}

	if err := domain.ValidateInventoryPrice(req.PurchasePrice); err != nil {
		return nil, err
	}

	if err := domain.ValidateInventoryNotes(req.Notes); err != nil {
		return nil, err
	}

	// Parse dates
	purchaseDate, err := parseDate(req.PurchaseDate)
	if err != nil {
		return nil, err
	}

	expirationDate, err := parseDate(req.ExpirationDate)
	if err != nil {
		return nil, err
	}

	// Create inventory item
	item := domain.NewInventoryItem(
		pantryID,
		req.LocationID,
		req.ProductVariantID,
		req.Quantity,
		req.UnitOfMeasureID,
		purchaseDate,
		expirationDate,
		req.PurchasePrice,
		req.Notes,
	)

	// Save to repository
	if err := uc.inventoryRepo.Create(item); err != nil {
		uc.logger.Error("Failed to create inventory item", err, map[string]interface{}{
			"user_id":            userID,
			"pantry_id":          pantryID,
			"product_variant_id": req.ProductVariantID,
		})
		return nil, err
	}

	// Dispatch domain events
	uc.eventDispatcher.DispatchEvents(ctx, item.GetEvents())
	item.ClearEvents()

	uc.logger.Info("Inventory item created successfully", map[string]interface{}{
		"user_id":   userID,
		"item_id":   item.ID,
		"pantry_id": item.PantryID,
		"quantity":  item.Quantity,
	})

	return item, nil
}

// GetInventoryItem retrieves an inventory item by ID
func (uc *InventoryUsecase) GetInventoryItem(ctx context.Context, userID, itemID uuid.UUID) (*domain.InventoryItem, error) {
	// Get the item first to check pantry access
	item, err := uc.inventoryRepo.GetByID(itemID)
	if err != nil {
		return nil, err
	}

	// Check if user can view items in this pantry
	canView, err := uc.authzService.CheckPermission(userID, item.PantryID, domain.PermissionViewItems)
	if err != nil {
		uc.logger.Error("Failed to check view items permission", err, map[string]interface{}{
			"user_id":   userID,
			"item_id":   itemID,
			"pantry_id": item.PantryID,
		})
		return nil, errors.ErrInternalError
	}

	if !canView {
		return nil, errors.ErrForbidden
	}

	return item, nil
}

// GetPantryInventory retrieves inventory items for a pantry with pagination
func (uc *InventoryUsecase) GetPantryInventory(ctx context.Context, userID, pantryID uuid.UUID, page, limit int) ([]*domain.InventoryItem, int64, error) {
	// Check if user can view items in this pantry
	canView, err := uc.authzService.CheckPermission(userID, pantryID, domain.PermissionViewItems)
	if err != nil {
		uc.logger.Error("Failed to check view items permission", err, map[string]interface{}{
			"user_id":   userID,
			"pantry_id": pantryID,
		})
		return nil, 0, errors.ErrInternalError
	}

	if !canView {
		return nil, 0, errors.ErrForbidden
	}

	// Get inventory items
	items, total, err := uc.inventoryRepo.GetByPantryID(pantryID, page, limit)
	if err != nil {
		uc.logger.Error("Failed to get pantry inventory", err, map[string]interface{}{
			"user_id":   userID,
			"pantry_id": pantryID,
		})
		return nil, 0, err
	}

	return items, total, nil
}

// UpdateInventoryItem updates an existing inventory item
func (uc *InventoryUsecase) UpdateInventoryItem(ctx context.Context, userID, itemID uuid.UUID, req *domain.UpdateInventoryItemRequest) (*domain.InventoryItem, error) {
	// Get the item first to check pantry access
	item, err := uc.inventoryRepo.GetByID(itemID)
	if err != nil {
		return nil, err
	}

	// Check if user can edit items in this pantry
	canEdit, err := uc.authzService.CheckPermission(userID, item.PantryID, domain.PermissionEditItems)
	if err != nil {
		uc.logger.Error("Failed to check edit items permission", err, map[string]interface{}{
			"user_id":   userID,
			"item_id":   itemID,
			"pantry_id": item.PantryID,
		})
		return nil, errors.ErrInternalError
	}

	if !canEdit {
		return nil, errors.ErrForbidden
	}

	// Validate location if provided
	if req.LocationID != nil {
		location, err := uc.locationRepo.GetByID(*req.LocationID)
		if err != nil {
			return nil, err
		}
		// Ensure location belongs to the same pantry
		if location.PantryID != item.PantryID {
			return nil, errors.New(errors.ErrCodeInvalidInput, "Location does not belong to the specified pantry")
		}
	}

	// Validate request data
	if err := domain.ValidateInventoryQuantity(req.Quantity); err != nil {
		return nil, err
	}

	if err := domain.ValidateInventoryPrice(req.PurchasePrice); err != nil {
		return nil, err
	}

	if err := domain.ValidateInventoryNotes(req.Notes); err != nil {
		return nil, err
	}

	// Parse dates
	purchaseDate, err := parseDate(req.PurchaseDate)
	if err != nil {
		return nil, err
	}

	expirationDate, err := parseDate(req.ExpirationDate)
	if err != nil {
		return nil, err
	}

	// Update item details
	if err := item.UpdateDetails(
		req.Quantity,
		req.LocationID,
		purchaseDate,
		expirationDate,
		req.PurchasePrice,
		req.Notes,
	); err != nil {
		return nil, err
	}

	// Save to repository
	if err := uc.inventoryRepo.Update(item); err != nil {
		uc.logger.Error("Failed to update inventory item", err, map[string]interface{}{
			"user_id": userID,
			"item_id": itemID,
		})
		return nil, err
	}

	// Dispatch domain events
	uc.eventDispatcher.DispatchEvents(ctx, item.GetEvents())
	item.ClearEvents()

	uc.logger.Info("Inventory item updated successfully", map[string]interface{}{
		"user_id": userID,
		"item_id": itemID,
	})

	return item, nil
}

// ConsumeInventoryItem consumes a quantity from an inventory item
func (uc *InventoryUsecase) ConsumeInventoryItem(ctx context.Context, userID, itemID uuid.UUID, req *domain.ConsumeInventoryRequest) (*domain.InventoryItem, error) {
	// Get the item first to check pantry access
	item, err := uc.inventoryRepo.GetByID(itemID)
	if err != nil {
		return nil, err
	}

	// Check if user can edit items in this pantry
	canEdit, err := uc.authzService.CheckPermission(userID, item.PantryID, domain.PermissionEditItems)
	if err != nil {
		uc.logger.Error("Failed to check edit items permission", err, map[string]interface{}{
			"user_id":   userID,
			"item_id":   itemID,
			"pantry_id": item.PantryID,
		})
		return nil, errors.ErrInternalError
	}

	if !canEdit {
		return nil, errors.ErrForbidden
	}

	// Consume quantity
	if err := item.ConsumeQuantity(req.ConsumedQuantity); err != nil {
		return nil, err
	}

	// Save to repository
	if err := uc.inventoryRepo.Update(item); err != nil {
		uc.logger.Error("Failed to update inventory item after consumption", err, map[string]interface{}{
			"user_id":           userID,
			"item_id":           itemID,
			"consumed_quantity": req.ConsumedQuantity,
		})
		return nil, err
	}

	// Dispatch domain events
	uc.eventDispatcher.DispatchEvents(ctx, item.GetEvents())
	item.ClearEvents()

	uc.logger.Info("Inventory item consumed successfully", map[string]interface{}{
		"user_id":           userID,
		"item_id":           itemID,
		"consumed_quantity": req.ConsumedQuantity,
		"remaining":         item.Quantity,
	})

	return item, nil
}

// BulkCreateInventoryItems creates multiple inventory items in a single transaction
func (uc *InventoryUsecase) BulkCreateInventoryItems(ctx context.Context, userID, pantryID uuid.UUID, req *domain.BulkCreateInventoryItemsRequest) (*domain.BulkCreateInventoryItemsResponse, error) {
	// Check if user can add items to pantry
	canAdd, err := uc.authzService.CheckPermission(userID, pantryID, domain.PermissionCreateItems)
	if err != nil {
		uc.logger.Error("Failed to check add items permission for bulk create", err, map[string]interface{}{
			"user_id":   userID,
			"pantry_id": pantryID,
		})
		return nil, errors.ErrInternalError
	}

	if !canAdd {
		return nil, errors.ErrForbidden
	}

	// Validate pantry exists
	_, err = uc.pantryRepo.GetByID(pantryID)
	if err != nil {
		return nil, err
	}

	response := &domain.BulkCreateInventoryItemsResponse{
		TotalCount: len(req.Items),
		Results:    make([]domain.BulkInventoryItemOperationResult, len(req.Items)),
		Summary: domain.BulkOperationSummary{
			TotalItems:       len(req.Items),
			AffectedPantries: []uuid.UUID{pantryID},
		},
	}

	// Process each item
	for i, itemReq := range req.Items {
		result := domain.BulkInventoryItemOperationResult{
			Index: i,
		}

		// Create individual item
		item, err := uc.createSingleInventoryItem(ctx, userID, pantryID, &itemReq)
		if err != nil {
			result.Success = false
			result.Error = &domain.BulkOperationError{
				Code:    uc.mapErrorToBulkCode(err),
				Message: err.Error(),
			}
			response.FailureCount++
		} else {
			result.Success = true
			result.ItemID = &item.ID
			result.Item = uc.convertToResponse(item)
			response.SuccessCount++
			response.Summary.TotalQuantityAdded += item.Quantity
		}

		response.Results[i] = result
	}

	// Calculate success rate
	if response.TotalCount > 0 {
		response.Summary.SuccessfulItems = response.SuccessCount
		response.Summary.FailedItems = response.FailureCount
		response.Summary.SuccessRate = float64(response.SuccessCount) / float64(response.TotalCount) * 100
	}

	uc.logger.Info("Bulk create inventory items completed", map[string]interface{}{
		"user_id":       userID,
		"pantry_id":     pantryID,
		"total_items":   response.TotalCount,
		"success_count": response.SuccessCount,
		"failure_count": response.FailureCount,
		"success_rate":  response.Summary.SuccessRate,
	})

	return response, nil
}

// BulkUpdateInventoryItems updates multiple inventory items
func (uc *InventoryUsecase) BulkUpdateInventoryItems(ctx context.Context, userID uuid.UUID, req *domain.BulkUpdateInventoryItemsRequest) (*domain.BulkUpdateInventoryItemsResponse, error) {
	response := &domain.BulkUpdateInventoryItemsResponse{
		TotalCount: len(req.Updates),
		Results:    make([]domain.BulkInventoryItemOperationResult, len(req.Updates)),
		Summary: domain.BulkOperationSummary{
			TotalItems: len(req.Updates),
		},
	}

	affectedPantries := make(map[uuid.UUID]bool)

	// Process each update
	for i, updateReq := range req.Updates {
		result := domain.BulkInventoryItemOperationResult{
			Index:  i,
			ItemID: &updateReq.ItemID,
		}

		// Update individual item
		item, err := uc.UpdateInventoryItem(ctx, userID, updateReq.ItemID, &updateReq.Updates)
		if err != nil {
			result.Success = false
			result.Error = &domain.BulkOperationError{
				Code:    uc.mapErrorToBulkCode(err),
				Message: err.Error(),
			}
			response.FailureCount++
		} else {
			result.Success = true
			result.Item = uc.convertToResponse(item)
			response.SuccessCount++
			affectedPantries[item.PantryID] = true
		}

		response.Results[i] = result
	}

	// Set affected pantries
	for pantryID := range affectedPantries {
		response.Summary.AffectedPantries = append(response.Summary.AffectedPantries, pantryID)
	}

	// Calculate success rate
	if response.TotalCount > 0 {
		response.Summary.SuccessfulItems = response.SuccessCount
		response.Summary.FailedItems = response.FailureCount
		response.Summary.SuccessRate = float64(response.SuccessCount) / float64(response.TotalCount) * 100
	}

	uc.logger.Info("Bulk update inventory items completed", map[string]interface{}{
		"user_id":       userID,
		"total_items":   response.TotalCount,
		"success_count": response.SuccessCount,
		"failure_count": response.FailureCount,
		"success_rate":  response.Summary.SuccessRate,
	})

	return response, nil
}

// BulkConsumeInventoryItems consumes quantities from multiple inventory items
func (uc *InventoryUsecase) BulkConsumeInventoryItems(ctx context.Context, userID uuid.UUID, req *domain.BulkConsumeInventoryRequest) (*domain.BulkConsumeInventoryResponse, error) {
	response := &domain.BulkConsumeInventoryResponse{
		TotalCount: len(req.Consumptions),
		Results:    make([]domain.BulkInventoryItemOperationResult, len(req.Consumptions)),
		Summary: domain.BulkOperationSummary{
			TotalItems: len(req.Consumptions),
		},
	}

	affectedPantries := make(map[uuid.UUID]bool)

	// Process each consumption
	for i, consumeReq := range req.Consumptions {
		result := domain.BulkInventoryItemOperationResult{
			Index:  i,
			ItemID: &consumeReq.ItemID,
		}

		// Create consume request
		var notes *string
		if consumeReq.Notes != "" {
			notes = &consumeReq.Notes
		}
		consumeRequest := &domain.ConsumeInventoryRequest{
			ConsumedQuantity: consumeReq.ConsumedQuantity,
			Notes:            notes,
		}

		// Consume from individual item
		item, err := uc.ConsumeInventoryItem(ctx, userID, consumeReq.ItemID, consumeRequest)
		if err != nil {
			result.Success = false
			result.Error = &domain.BulkOperationError{
				Code:    uc.mapErrorToBulkCode(err),
				Message: err.Error(),
			}
			response.FailureCount++
		} else {
			result.Success = true
			result.Item = uc.convertToResponse(item)
			response.SuccessCount++
			response.Summary.TotalQuantityConsumed += consumeReq.ConsumedQuantity
			affectedPantries[item.PantryID] = true
		}

		response.Results[i] = result
	}

	// Set affected pantries
	for pantryID := range affectedPantries {
		response.Summary.AffectedPantries = append(response.Summary.AffectedPantries, pantryID)
	}

	// Calculate success rate
	if response.TotalCount > 0 {
		response.Summary.SuccessfulItems = response.SuccessCount
		response.Summary.FailedItems = response.FailureCount
		response.Summary.SuccessRate = float64(response.SuccessCount) / float64(response.TotalCount) * 100
	}

	uc.logger.Info("Bulk consume inventory items completed", map[string]interface{}{
		"user_id":        userID,
		"total_items":    response.TotalCount,
		"success_count":  response.SuccessCount,
		"failure_count":  response.FailureCount,
		"success_rate":   response.Summary.SuccessRate,
		"total_consumed": response.Summary.TotalQuantityConsumed,
	})

	return response, nil
}

// BulkDeleteInventoryItems deletes multiple inventory items
func (uc *InventoryUsecase) BulkDeleteInventoryItems(ctx context.Context, userID uuid.UUID, req *domain.BulkDeleteInventoryItemsRequest) (*domain.BulkDeleteInventoryItemsResponse, error) {
	response := &domain.BulkDeleteInventoryItemsResponse{
		TotalCount: len(req.ItemIDs),
		Results:    make([]domain.BulkInventoryItemOperationResult, len(req.ItemIDs)),
		Summary: domain.BulkOperationSummary{
			TotalItems: len(req.ItemIDs),
		},
	}

	affectedPantries := make(map[uuid.UUID]bool)

	// Process each deletion
	for i, itemID := range req.ItemIDs {
		result := domain.BulkInventoryItemOperationResult{
			Index:  i,
			ItemID: &itemID,
		}

		// Get the item first to check pantry access
		item, err := uc.inventoryRepo.GetByID(itemID)
		if err != nil {
			result.Success = false
			result.Error = &domain.BulkOperationError{
				Code:    uc.mapErrorToBulkCode(err),
				Message: err.Error(),
			}
			response.FailureCount++
			response.Results[i] = result
			continue
		}

		// Check if user can delete items in this pantry
		canDelete, err := uc.authzService.CheckPermission(userID, item.PantryID, domain.PermissionDeleteItems)
		if err != nil {
			result.Success = false
			result.Error = &domain.BulkOperationError{
				Code:    "PERMISSION_CHECK_FAILED",
				Message: "Failed to check delete permission",
			}
			response.FailureCount++
			response.Results[i] = result
			continue
		}

		if !canDelete {
			result.Success = false
			result.Error = &domain.BulkOperationError{
				Code:    "FORBIDDEN",
				Message: "Insufficient permissions to delete item",
			}
			response.FailureCount++
			response.Results[i] = result
			continue
		}

		// Soft delete the item
		item.SoftDelete()

		// Save to repository
		if err := uc.inventoryRepo.Update(item); err != nil {
			result.Success = false
			result.Error = &domain.BulkOperationError{
				Code:    "UPDATE_FAILED",
				Message: "Failed to save deletion",
			}
			response.FailureCount++
		} else {
			result.Success = true
			response.SuccessCount++
			affectedPantries[item.PantryID] = true

			// Dispatch domain events
			uc.eventDispatcher.DispatchEvents(ctx, item.GetEvents())
			item.ClearEvents()
		}

		response.Results[i] = result
	}

	// Set affected pantries
	for pantryID := range affectedPantries {
		response.Summary.AffectedPantries = append(response.Summary.AffectedPantries, pantryID)
	}

	// Calculate success rate
	if response.TotalCount > 0 {
		response.Summary.SuccessfulItems = response.SuccessCount
		response.Summary.FailedItems = response.FailureCount
		response.Summary.SuccessRate = float64(response.SuccessCount) / float64(response.TotalCount) * 100
	}

	uc.logger.Info("Bulk delete inventory items completed", map[string]interface{}{
		"user_id":       userID,
		"total_items":   response.TotalCount,
		"success_count": response.SuccessCount,
		"failure_count": response.FailureCount,
		"success_rate":  response.Summary.SuccessRate,
	})

	return response, nil
}

// Helper functions for bulk operations

// createSingleInventoryItem creates a single inventory item (used by bulk operations)
func (uc *InventoryUsecase) createSingleInventoryItem(ctx context.Context, userID, pantryID uuid.UUID, req *domain.CreateInventoryItemRequest) (*domain.InventoryItem, error) {
	// Validate product variant exists
	_, err := uc.productVariantRepo.GetByID(req.ProductVariantID)
	if err != nil {
		return nil, err
	}

	// Validate unit of measure exists
	_, err = uc.unitRepo.GetByID(req.UnitOfMeasureID)
	if err != nil {
		return nil, err
	}

	// Validate location if provided
	if req.LocationID != nil {
		location, err := uc.locationRepo.GetByID(*req.LocationID)
		if err != nil {
			return nil, err
		}
		// Ensure location belongs to the same pantry
		if location.PantryID != pantryID {
			return nil, errors.New(errors.ErrCodeInvalidInput, "Location does not belong to the specified pantry")
		}
	}

	// Validate request data
	if err := domain.ValidateInventoryQuantity(req.Quantity); err != nil {
		return nil, err
	}

	if err := domain.ValidateInventoryPrice(req.PurchasePrice); err != nil {
		return nil, err
	}

	if err := domain.ValidateInventoryNotes(req.Notes); err != nil {
		return nil, err
	}

	// Parse dates
	purchaseDate, err := parseDate(req.PurchaseDate)
	if err != nil {
		return nil, err
	}

	expirationDate, err := parseDate(req.ExpirationDate)
	if err != nil {
		return nil, err
	}

	// Create inventory item
	item := domain.NewInventoryItem(
		pantryID,
		req.LocationID,
		req.ProductVariantID,
		req.Quantity,
		req.UnitOfMeasureID,
		purchaseDate,
		expirationDate,
		req.PurchasePrice,
		req.Notes,
	)

	// Save to repository
	if err := uc.inventoryRepo.Create(item); err != nil {
		return nil, err
	}

	// Dispatch domain events
	uc.eventDispatcher.DispatchEvents(ctx, item.GetEvents())
	item.ClearEvents()

	return item, nil
}

// mapErrorToBulkCode maps errors to bulk operation error codes
func (uc *InventoryUsecase) mapErrorToBulkCode(err error) string {
	if appErr := errors.GetAppError(err); appErr != nil {
		switch appErr.Code {
		case errors.ErrCodeNotFound:
			return "NOT_FOUND"
		case errors.ErrCodeInvalidInput:
			return "INVALID_INPUT"
		case errors.ErrCodeForbidden:
			return "FORBIDDEN"
		case errors.ErrCodeConflict:
			return "CONFLICT"
		default:
			return "INTERNAL_ERROR"
		}
	}
	return "UNKNOWN_ERROR"
}

// convertToResponse converts a domain inventory item to response format
func (uc *InventoryUsecase) convertToResponse(item *domain.InventoryItem) *domain.InventoryItemResponse {
	response := &domain.InventoryItemResponse{
		ID:               item.ID,
		PantryID:         item.PantryID,
		LocationID:       item.LocationID,
		ProductVariantID: item.ProductVariantID,
		Quantity:         item.Quantity,
		UnitOfMeasureID:  item.UnitOfMeasureID,
		PurchasePrice:    item.PurchasePrice,
		Notes:            item.Notes,
		Status:           item.GetStatus(),
		CreatedAt:        item.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:        item.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}

	// Convert dates to string pointers
	if item.PurchaseDate != nil {
		dateStr := item.PurchaseDate.Format("2006-01-02")
		response.PurchaseDate = &dateStr
	}

	if item.ExpirationDate != nil {
		dateStr := item.ExpirationDate.Format("2006-01-02")
		response.ExpirationDate = &dateStr
	}

	return response
}

// Helper functions for date parsing

// parseDate parses a date string in YYYY-MM-DD format to time.Time
func parseDate(dateStr *string) (*time.Time, error) {
	if dateStr == nil || *dateStr == "" {
		return nil, nil
	}

	parsedTime, err := time.Parse("2006-01-02", *dateStr)
	if err != nil {
		return nil, errors.New(errors.ErrCodeInvalidInput, "Invalid date format. Expected YYYY-MM-DD")
	}

	return &parsedTime, nil
}
