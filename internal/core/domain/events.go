package domain

import (
	"time"

	"github.com/google/uuid"
)

// DomainEvent represents a domain event that occurred in the system
type DomainEvent interface {
	GetEventID() uuid.UUID
	GetEventType() string
	GetAggregateID() uuid.UUID
	GetOccurredAt() time.Time
	GetEventData() map[string]interface{}
}

// BaseDomainEvent provides common functionality for domain events
type BaseDomainEvent struct {
	EventID     uuid.UUID              `json:"event_id"`
	EventType   string                 `json:"event_type"`
	AggregateID uuid.UUID              `json:"aggregate_id"`
	OccurredAt  time.Time              `json:"occurred_at"`
	EventData   map[string]interface{} `json:"event_data"`
}

// GetEventID returns the event ID
func (e *BaseDomainEvent) GetEventID() uuid.UUID {
	return e.EventID
}

// GetEventType returns the event type
func (e *BaseDomainEvent) GetEventType() string {
	return e.EventType
}

// GetAggregateID returns the aggregate ID
func (e *BaseDomainEvent) GetAggregateID() uuid.UUID {
	return e.AggregateID
}

// GetOccurredAt returns when the event occurred
func (e *BaseDomainEvent) GetOccurredAt() time.Time {
	return e.OccurredAt
}

// GetEventData returns the event data
func (e *BaseDomainEvent) GetEventData() map[string]interface{} {
	return e.EventData
}

// newBaseDomainEvent creates a new base domain event
func newBaseDomainEvent(eventType string, aggregateID uuid.UUID, eventData map[string]interface{}) *BaseDomainEvent {
	return &BaseDomainEvent{
		EventID:     uuid.New(),
		EventType:   eventType,
		AggregateID: aggregateID,
		OccurredAt:  time.Now(),
		EventData:   eventData,
	}
}

// User Events

// UserRegisteredEvent represents a user registration event
type UserRegisteredEvent struct {
	*BaseDomainEvent
}

// NewUserRegisteredEvent creates a new user registered event
func NewUserRegisteredEvent(userID uuid.UUID, email, username string) *UserRegisteredEvent {
	eventData := map[string]interface{}{
		"email":    email,
		"username": username,
	}

	return &UserRegisteredEvent{
		BaseDomainEvent: newBaseDomainEvent("user.registered", userID, eventData),
	}
}

// UserProfileUpdatedEvent represents a user profile update event
type UserProfileUpdatedEvent struct {
	*BaseDomainEvent
}

// NewUserProfileUpdatedEvent creates a new user profile updated event
func NewUserProfileUpdatedEvent(userID uuid.UUID, email string) *UserProfileUpdatedEvent {
	eventData := map[string]interface{}{
		"email": email,
	}

	return &UserProfileUpdatedEvent{
		BaseDomainEvent: newBaseDomainEvent("user.profile_updated", userID, eventData),
	}
}

// UserPasswordChangedEvent represents a password change event
type UserPasswordChangedEvent struct {
	*BaseDomainEvent
}

// NewUserPasswordChangedEvent creates a new user password changed event
func NewUserPasswordChangedEvent(userID uuid.UUID, email string) *UserPasswordChangedEvent {
	eventData := map[string]interface{}{
		"email": email,
	}

	return &UserPasswordChangedEvent{
		BaseDomainEvent: newBaseDomainEvent("user.password_changed", userID, eventData),
	}
}

// UserDeletedEvent represents a user deletion event
type UserDeletedEvent struct {
	*BaseDomainEvent
}

// NewUserDeletedEvent creates a new user deleted event
func NewUserDeletedEvent(userID uuid.UUID, email string) *UserDeletedEvent {
	eventData := map[string]interface{}{
		"email": email,
	}

	return &UserDeletedEvent{
		BaseDomainEvent: newBaseDomainEvent("user.deleted", userID, eventData),
	}
}

// Authentication Events

// UserLoggedInEvent represents a user login event
type UserLoggedInEvent struct {
	*BaseDomainEvent
}

// NewUserLoggedInEvent creates a new user logged in event
func NewUserLoggedInEvent(userID uuid.UUID, email, clientIP, userAgent string) *UserLoggedInEvent {
	eventData := map[string]interface{}{
		"email":      email,
		"client_ip":  clientIP,
		"user_agent": userAgent,
	}

	return &UserLoggedInEvent{
		BaseDomainEvent: newBaseDomainEvent("user.logged_in", userID, eventData),
	}
}

// UserLoggedOutEvent represents a user logout event
type UserLoggedOutEvent struct {
	*BaseDomainEvent
}

// NewUserLoggedOutEvent creates a new user logged out event
func NewUserLoggedOutEvent(userID uuid.UUID, email string) *UserLoggedOutEvent {
	eventData := map[string]interface{}{
		"email": email,
	}

	return &UserLoggedOutEvent{
		BaseDomainEvent: newBaseDomainEvent("user.logged_out", userID, eventData),
	}
}

// RefreshTokenCreatedEvent represents a refresh token creation event
type RefreshTokenCreatedEvent struct {
	*BaseDomainEvent
}

// NewRefreshTokenCreatedEvent creates a new refresh token created event
func NewRefreshTokenCreatedEvent(userID uuid.UUID, tokenID uuid.UUID, expiresAt time.Time) *RefreshTokenCreatedEvent {
	eventData := map[string]interface{}{
		"token_id":   tokenID,
		"expires_at": expiresAt,
	}

	return &RefreshTokenCreatedEvent{
		BaseDomainEvent: newBaseDomainEvent("refresh_token.created", userID, eventData),
	}
}

// RefreshTokenRevokedEvent represents a refresh token revocation event
type RefreshTokenRevokedEvent struct {
	*BaseDomainEvent
}

// NewRefreshTokenRevokedEvent creates a new refresh token revoked event
func NewRefreshTokenRevokedEvent(userID uuid.UUID, tokenID uuid.UUID, reason string) *RefreshTokenRevokedEvent {
	eventData := map[string]interface{}{
		"token_id": tokenID,
		"reason":   reason,
	}

	return &RefreshTokenRevokedEvent{
		BaseDomainEvent: newBaseDomainEvent("refresh_token.revoked", userID, eventData),
	}
}

// Pantry Events

// PantryCreatedEvent represents a pantry creation event
type PantryCreatedEvent struct {
	*BaseDomainEvent
}

// NewPantryCreatedEvent creates a new pantry created event
func NewPantryCreatedEvent(pantryID uuid.UUID, ownerUserID uuid.UUID, name string) *PantryCreatedEvent {
	eventData := map[string]interface{}{
		"owner_user_id": ownerUserID,
		"name":          name,
	}

	return &PantryCreatedEvent{
		BaseDomainEvent: newBaseDomainEvent("pantry.created", pantryID, eventData),
	}
}

// PantryUpdatedEvent represents a pantry update event
type PantryUpdatedEvent struct {
	*BaseDomainEvent
}

// NewPantryUpdatedEvent creates a new pantry updated event
func NewPantryUpdatedEvent(pantryID uuid.UUID, ownerUserID uuid.UUID, name string) *PantryUpdatedEvent {
	eventData := map[string]interface{}{
		"owner_user_id": ownerUserID,
		"name":          name,
	}

	return &PantryUpdatedEvent{
		BaseDomainEvent: newBaseDomainEvent("pantry.updated", pantryID, eventData),
	}
}

// PantryDeletedEvent represents a pantry deletion event
type PantryDeletedEvent struct {
	*BaseDomainEvent
}

// NewPantryDeletedEvent creates a new pantry deleted event
func NewPantryDeletedEvent(pantryID uuid.UUID, ownerUserID uuid.UUID) *PantryDeletedEvent {
	eventData := map[string]interface{}{
		"owner_user_id": ownerUserID,
	}

	return &PantryDeletedEvent{
		BaseDomainEvent: newBaseDomainEvent("pantry.deleted", pantryID, eventData),
	}
}

// PantryOwnershipTransferredEvent represents a pantry ownership transfer event
type PantryOwnershipTransferredEvent struct {
	*BaseDomainEvent
}

// NewPantryOwnershipTransferredEvent creates a new ownership transferred event
func NewPantryOwnershipTransferredEvent(pantryID uuid.UUID, oldOwnerUserID, newOwnerUserID uuid.UUID) *PantryOwnershipTransferredEvent {
	eventData := map[string]interface{}{
		"old_owner_user_id": oldOwnerUserID,
		"new_owner_user_id": newOwnerUserID,
	}

	return &PantryOwnershipTransferredEvent{
		BaseDomainEvent: newBaseDomainEvent("pantry.ownership_transferred", pantryID, eventData),
	}
}

// Pantry Membership Events

// PantryMemberInvitedEvent represents a pantry member invitation event
type PantryMemberInvitedEvent struct {
	*BaseDomainEvent
}

// NewPantryMemberInvitedEvent creates a new member invited event
func NewPantryMemberInvitedEvent(pantryID uuid.UUID, userID uuid.UUID, role string, invitedByUserID *uuid.UUID) *PantryMemberInvitedEvent {
	eventData := map[string]interface{}{
		"user_id": userID,
		"role":    string(role),
	}

	if invitedByUserID != nil {
		eventData["invited_by_user_id"] = *invitedByUserID
	}

	return &PantryMemberInvitedEvent{
		BaseDomainEvent: newBaseDomainEvent("pantry.member_invited", pantryID, eventData),
	}
}

// PantryMemberJoinedEvent represents a pantry member joined event
type PantryMemberJoinedEvent struct {
	*BaseDomainEvent
}

// NewPantryMemberJoinedEvent creates a new member joined event
func NewPantryMemberJoinedEvent(pantryID uuid.UUID, userID uuid.UUID, role string) *PantryMemberJoinedEvent {
	eventData := map[string]interface{}{
		"user_id": userID,
		"role":    role,
	}

	return &PantryMemberJoinedEvent{
		BaseDomainEvent: newBaseDomainEvent("pantry.member_joined", pantryID, eventData),
	}
}

// PantryMemberRoleUpdatedEvent represents a pantry member role update event
type PantryMemberRoleUpdatedEvent struct {
	*BaseDomainEvent
}

// NewPantryMemberRoleUpdatedEvent creates a new member role updated event
func NewPantryMemberRoleUpdatedEvent(pantryID uuid.UUID, userID uuid.UUID, oldRole, newRole string) *PantryMemberRoleUpdatedEvent {
	eventData := map[string]interface{}{
		"user_id":  userID,
		"old_role": oldRole,
		"new_role": newRole,
	}

	return &PantryMemberRoleUpdatedEvent{
		BaseDomainEvent: newBaseDomainEvent("pantry.member_role_updated", pantryID, eventData),
	}
}

// PantryMemberRemovedEvent represents a pantry member removal event
type PantryMemberRemovedEvent struct {
	*BaseDomainEvent
}

// NewPantryMemberRemovedEvent creates a new member removed event
func NewPantryMemberRemovedEvent(pantryID uuid.UUID, userID uuid.UUID, role string, reason string) *PantryMemberRemovedEvent {
	eventData := map[string]interface{}{
		"user_id": userID,
		"role":    role,
		"reason":  reason,
	}

	return &PantryMemberRemovedEvent{
		BaseDomainEvent: newBaseDomainEvent("pantry.member_removed", pantryID, eventData),
	}
}

// Pantry Location Events

// PantryLocationCreatedEvent represents a pantry location creation event
type PantryLocationCreatedEvent struct {
	*BaseDomainEvent
}

// NewPantryLocationCreatedEvent creates a new location created event
func NewPantryLocationCreatedEvent(locationID uuid.UUID, pantryID uuid.UUID, name string) *PantryLocationCreatedEvent {
	eventData := map[string]interface{}{
		"pantry_id": pantryID,
		"name":      name,
	}

	return &PantryLocationCreatedEvent{
		BaseDomainEvent: newBaseDomainEvent("pantry.location_created", locationID, eventData),
	}
}

// PantryLocationUpdatedEvent represents a pantry location update event
type PantryLocationUpdatedEvent struct {
	*BaseDomainEvent
}

// NewPantryLocationUpdatedEvent creates a new location updated event
func NewPantryLocationUpdatedEvent(locationID uuid.UUID, pantryID uuid.UUID, name string) *PantryLocationUpdatedEvent {
	eventData := map[string]interface{}{
		"pantry_id": pantryID,
		"name":      name,
	}

	return &PantryLocationUpdatedEvent{
		BaseDomainEvent: newBaseDomainEvent("pantry.location_updated", locationID, eventData),
	}
}

// PantryLocationDeletedEvent represents a pantry location deletion event
type PantryLocationDeletedEvent struct {
	*BaseDomainEvent
}

// NewPantryLocationDeletedEvent creates a new location deleted event
func NewPantryLocationDeletedEvent(locationID uuid.UUID, pantryID uuid.UUID) *PantryLocationDeletedEvent {
	eventData := map[string]interface{}{
		"pantry_id": pantryID,
	}

	return &PantryLocationDeletedEvent{
		BaseDomainEvent: newBaseDomainEvent("pantry.location_deleted", locationID, eventData),
	}
}

// EventDispatcher defines the interface for dispatching domain events
type EventDispatcher interface {
	Dispatch(events []DomainEvent) error
}

// EventHandler defines the interface for handling domain events
type EventHandler interface {
	Handle(event DomainEvent) error
	CanHandle(eventType string) bool
}
