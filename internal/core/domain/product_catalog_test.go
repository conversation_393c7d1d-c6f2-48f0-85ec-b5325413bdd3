package domain

import (
	"testing"
	"time"

	"github.com/google/uuid"
)

func TestNewCategory(t *testing.T) {
	name := "Dairy Products"
	description := "Milk, cheese, yogurt, etc."
	parentID := uuid.New()
	
	category := NewCategory(name, &description, &parentID)
	
	// Test basic fields
	if category.Name != name {
		t.<PERSON>("Expected name %s, got %s", name, category.Name)
	}
	
	if category.Description == nil || *category.Description != description {
		t.<PERSON>rf("Expected description %s, got %v", description, category.Description)
	}
	
	if category.ParentCategoryID == nil || *category.ParentCategoryID != parentID {
		t.<PERSON>rf("Expected parent ID %s, got %v", parentID, category.ParentCategoryID)
	}
	
	// Test generated fields
	if category.ID == uuid.Nil {
		t.<PERSON>r("Expected non-nil UUID")
	}
	
	if category.CreatedAt.IsZero() {
		t.<PERSON>r("Expected non-zero CreatedAt")
	}
	
	if category.UpdatedAt.IsZero() {
		t.Error("Expected non-zero UpdatedAt")
	}
	
	// Test events
	events := category.GetEvents()
	if len(events) != 1 {
		t.<PERSON><PERSON>("Expected 1 event, got %d", len(events))
	}
	
	if events[0].GetEventType() != "category.created" {
		t.Errorf("Expected event type 'category.created', got %s", events[0].GetEventType())
	}
}

func TestCategoryUpdateDetails(t *testing.T) {
	category := NewCategory("Original Name", nil, nil)
	category.ClearEvents() // Clear creation event
	
	newName := "Updated Name"
	newDescription := "Updated description"
	
	originalUpdatedAt := category.UpdatedAt
	time.Sleep(1 * time.Millisecond) // Ensure time difference
	
	category.UpdateDetails(newName, &newDescription)
	
	// Test updated fields
	if category.Name != newName {
		t.Errorf("Expected name %s, got %s", newName, category.Name)
	}
	
	if category.Description == nil || *category.Description != newDescription {
		t.Errorf("Expected description %s, got %v", newDescription, category.Description)
	}
	
	// Test updated timestamp
	if !category.UpdatedAt.After(originalUpdatedAt) {
		t.Error("Expected UpdatedAt to be updated")
	}
	
	// Test events
	events := category.GetEvents()
	if len(events) != 1 {
		t.Errorf("Expected 1 event, got %d", len(events))
	}
	
	if events[0].GetEventType() != "category.updated" {
		t.Errorf("Expected event type 'category.updated', got %s", events[0].GetEventType())
	}
}

func TestNewUnitOfMeasure(t *testing.T) {
	name := "Kilogram"
	symbol := "kg"
	unitType := UnitTypeWeight
	description := "Base unit for weight"
	
	unit := NewUnitOfMeasure(name, symbol, unitType, &description)
	
	// Test basic fields
	if unit.Name != name {
		t.Errorf("Expected name %s, got %s", name, unit.Name)
	}
	
	if unit.Symbol != symbol {
		t.Errorf("Expected symbol %s, got %s", symbol, unit.Symbol)
	}
	
	if unit.Type != unitType {
		t.Errorf("Expected type %s, got %s", unitType, unit.Type)
	}
	
	if unit.Description == nil || *unit.Description != description {
		t.Errorf("Expected description %s, got %v", description, unit.Description)
	}
	
	if !unit.IsBaseUnit {
		t.Error("Expected unit to be a base unit")
	}
	
	// Test generated fields
	if unit.ID == uuid.Nil {
		t.Error("Expected non-nil UUID")
	}
	
	// Test events
	events := unit.GetEvents()
	if len(events) != 1 {
		t.Errorf("Expected 1 event, got %d", len(events))
	}
	
	if events[0].GetEventType() != "unit_of_measure.created" {
		t.Errorf("Expected event type 'unit_of_measure.created', got %s", events[0].GetEventType())
	}
}

func TestNewDerivedUnitOfMeasure(t *testing.T) {
	baseUnitID := uuid.New()
	conversionFactor := 1000.0 // 1 kg = 1000 g
	
	unit := NewDerivedUnitOfMeasure("Gram", "g", UnitTypeWeight, nil, baseUnitID, conversionFactor)
	
	// Test derived unit fields
	if unit.IsBaseUnit {
		t.Error("Expected unit to not be a base unit")
	}
	
	if unit.BaseUnitID == nil || *unit.BaseUnitID != baseUnitID {
		t.Errorf("Expected base unit ID %s, got %v", baseUnitID, unit.BaseUnitID)
	}
	
	if unit.ConversionFactor == nil || *unit.ConversionFactor != conversionFactor {
		t.Errorf("Expected conversion factor %f, got %v", conversionFactor, unit.ConversionFactor)
	}
}

func TestUnitConversion(t *testing.T) {
	// Create base unit (kg)
	baseUnit := NewUnitOfMeasure("Kilogram", "kg", UnitTypeWeight, nil)
	
	// Create derived unit (g) - 1 kg = 1000 g
	derivedUnit := NewDerivedUnitOfMeasure("Gram", "g", UnitTypeWeight, nil, baseUnit.ID, 1000.0)
	
	// Test conversion to base unit
	quantity := 500.0 // 500 grams
	baseQuantity, err := derivedUnit.ConvertToBaseUnit(quantity)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
	
	expected := 0.5 // 0.5 kg
	if baseQuantity != expected {
		t.Errorf("Expected %f kg, got %f kg", expected, baseQuantity)
	}
	
	// Test conversion from base unit
	baseQuantity = 2.0 // 2 kg
	derivedQuantity, err := derivedUnit.ConvertFromBaseUnit(baseQuantity)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
	
	expected = 2000.0 // 2000 g
	if derivedQuantity != expected {
		t.Errorf("Expected %f g, got %f g", expected, derivedQuantity)
	}
}

func TestNewProduct(t *testing.T) {
	name := "Milk"
	description := "Fresh dairy milk"
	categoryID := uuid.New()
	brand := "Local Farm"
	
	product := NewProduct(name, &description, categoryID, &brand)
	
	// Test basic fields
	if product.Name != name {
		t.Errorf("Expected name %s, got %s", name, product.Name)
	}
	
	if product.Description == nil || *product.Description != description {
		t.Errorf("Expected description %s, got %v", description, product.Description)
	}
	
	if product.CategoryID != categoryID {
		t.Errorf("Expected category ID %s, got %s", categoryID, product.CategoryID)
	}
	
	if product.Brand == nil || *product.Brand != brand {
		t.Errorf("Expected brand %s, got %v", brand, product.Brand)
	}
	
	// Test generated fields
	if product.ID == uuid.Nil {
		t.Error("Expected non-nil UUID")
	}
	
	// Test events
	events := product.GetEvents()
	if len(events) != 1 {
		t.Errorf("Expected 1 event, got %d", len(events))
	}
	
	if events[0].GetEventType() != "product.created" {
		t.Errorf("Expected event type 'product.created', got %s", events[0].GetEventType())
	}
}

func TestNewProductVariant(t *testing.T) {
	productID := uuid.New()
	name := "Whole Milk 1 Gallon"
	description := "1 gallon container of whole milk"
	barcode := "1234567890123"
	imageURL := "https://example.com/milk.jpg"
	packagingType := PackagingTypeSingle
	unitID := uuid.New()
	
	variant := NewProductVariant(productID, name, &description, &barcode, &imageURL, packagingType, &unitID)
	
	// Test basic fields
	if variant.ProductID != productID {
		t.Errorf("Expected product ID %s, got %s", productID, variant.ProductID)
	}
	
	if variant.Name != name {
		t.Errorf("Expected name %s, got %s", name, variant.Name)
	}
	
	if variant.BarcodeGTIN == nil || *variant.BarcodeGTIN != barcode {
		t.Errorf("Expected barcode %s, got %v", barcode, variant.BarcodeGTIN)
	}
	
	if variant.PackagingType != packagingType {
		t.Errorf("Expected packaging type %s, got %s", packagingType, variant.PackagingType)
	}
	
	if !variant.HasBarcode() {
		t.Error("Expected variant to have barcode")
	}
	
	// Test generated fields
	if variant.ID == uuid.Nil {
		t.Error("Expected non-nil UUID")
	}
	
	// Test events
	events := variant.GetEvents()
	if len(events) != 1 {
		t.Errorf("Expected 1 event, got %d", len(events))
	}
	
	if events[0].GetEventType() != "product_variant.created" {
		t.Errorf("Expected event type 'product_variant.created', got %s", events[0].GetEventType())
	}
}

func TestValidateBarcodeGTIN(t *testing.T) {
	// Test valid barcodes
	validBarcodes := []string{
		"12345678",     // 8 digits
		"123456789012", // 12 digits
		"1234567890123", // 13 digits
		"12345678901234", // 14 digits
	}
	
	for _, barcode := range validBarcodes {
		if err := ValidateBarcodeGTIN(barcode); err != nil {
			t.Errorf("Expected barcode %s to be valid, got error: %v", barcode, err)
		}
	}
	
	// Test invalid barcodes
	invalidBarcodes := []string{
		"1234567",        // Too short
		"123456789012345", // Too long
		"12345678a012",   // Contains letter
		"",               // Empty
	}
	
	for _, barcode := range invalidBarcodes {
		if err := ValidateBarcodeGTIN(barcode); err == nil {
			t.Errorf("Expected barcode %s to be invalid", barcode)
		}
	}
}
