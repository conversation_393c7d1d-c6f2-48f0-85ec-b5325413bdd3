package config

import (
	"fmt"
	"strings"
	"time"

	"github.com/knadh/koanf/parsers/yaml"
	"github.com/knadh/koanf/providers/env"
	"github.com/knadh/koanf/providers/file"
	"github.com/knadh/koanf/v2"
)

// Config represents the application configuration
type Config struct {
	App      AppConfig      `koanf:"app"`
	Server   ServerConfig   `koanf:"server"`
	Database DatabaseConfig `koanf:"database"`
	Redis    RedisConfig    `koanf:"redis"`
	Auth     AuthConfig     `koanf:"auth"`
	Logger   LoggerConfig   `koanf:"logger"`
}

// AppConfig contains general application settings
type AppConfig struct {
	Name        string `koanf:"name"`
	Version     string `koanf:"version"`
	Environment string `koanf:"environment"`
}

// ServerConfig contains HTTP server configuration
type ServerConfig struct {
	Host         string          `koanf:"host"`
	Port         string          `koanf:"port"`
	ReadTimeout  string          `koanf:"read_timeout"`
	WriteTimeout string          `koanf:"write_timeout"`
	IdleTimeout  string          `koanf:"idle_timeout"`
	BodyLimit    string          `koanf:"body_limit"`
	Prefork      bool            `koanf:"prefork"`
	CORS         CORSConfig      `koanf:"cors"`
	RateLimit    RateLimitConfig `koanf:"rate_limit"`
}

// CORSConfig contains CORS configuration
type CORSConfig struct {
	AllowOrigins     []string `koanf:"allow_origins"`
	AllowMethods     []string `koanf:"allow_methods"`
	AllowHeaders     []string `koanf:"allow_headers"`
	AllowCredentials bool     `koanf:"allow_credentials"`
	ExposeHeaders    []string `koanf:"expose_headers"`
	MaxAge           int      `koanf:"max_age"`
}

// RateLimitConfig contains rate limiting configuration
type RateLimitConfig struct {
	Enabled    bool   `koanf:"enabled"`
	Max        int    `koanf:"max"`
	Expiration string `koanf:"expiration"`
}

// DatabaseConfig contains database connection settings
type DatabaseConfig struct {
	Host            string `koanf:"host"`
	Port            string `koanf:"port"`
	Username        string `koanf:"username"`
	Password        string `koanf:"password"`
	Database        string `koanf:"database"`
	SSLMode         string `koanf:"ssl_mode"`
	MaxIdleConns    int    `koanf:"max_idle_conns"`
	MaxOpenConns    int    `koanf:"max_open_conns"`
	ConnMaxLifetime string `koanf:"conn_max_lifetime"`
	LogLevel        string `koanf:"log_level"`
}

// RedisConfig contains Redis connection settings
type RedisConfig struct {
	Host         string            `koanf:"host"`
	Port         string            `koanf:"port"`
	Password     string            `koanf:"password"`
	Database     int               `koanf:"database"`
	PoolSize     int               `koanf:"pool_size"`
	MinIdleConns int               `koanf:"min_idle_conns"`
	MaxRetries   int               `koanf:"max_retries"`
	Idempotency  IdempotencyConfig `koanf:"idempotency"`
}

// IdempotencyConfig holds configuration for the idempotency service
type IdempotencyConfig struct {
	ProcessingTTL time.Duration `koanf:"processing_ttl"`
	ResponseTTL   time.Duration `koanf:"response_ttl"`
	KeyPrefix     string        `koanf:"key_prefix"`
}

// AuthConfig contains authentication settings
type AuthConfig struct {
	JWT JWTConfig `koanf:"jwt"`
}

// JWTConfig contains JWT-specific settings
type JWTConfig struct {
	Secret                 string `koanf:"secret"`
	AccessTokenExpiration  string `koanf:"access_token_expiration"`
	RefreshTokenExpiration string `koanf:"refresh_token_expiration"`
	Issuer                 string `koanf:"issuer"`
}

// LoggerConfig contains logging configuration
type LoggerConfig struct {
	Level      string `koanf:"level"`
	Format     string `koanf:"format"` // json or console
	TimeFormat string `koanf:"time_format"`
}

// Load loads configuration from file and environment variables
func Load() (*Config, error) {
	k := koanf.New(".")

	// Load from config file if it exists
	if err := k.Load(file.Provider("config.yaml"), yaml.Parser()); err != nil {
		// Config file is optional, so we only log the error
		fmt.Printf("Warning: Could not load config.yaml: %v\n", err)
	}

	// Load from environment variables with APP_ prefix
	if err := k.Load(env.Provider("APP_", ".", func(s string) string {
		return strings.Replace(strings.ToLower(
			strings.TrimPrefix(s, "APP_")), "_", ".", -1)
	}), nil); err != nil {
		return nil, fmt.Errorf("failed to load environment variables: %w", err)
	}

	// Set defaults
	setDefaults(k)

	var config Config
	if err := k.Unmarshal("", &config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Validate required fields
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	return &config, nil
}

// setDefaults sets default configuration values
func setDefaults(k *koanf.Koanf) {
	defaults := map[string]interface{}{
		"app.name":        "Pantry Pal",
		"app.version":     "1.0.0",
		"app.environment": "development",

		"server.host":          "0.0.0.0",
		"server.port":          "8080",
		"server.read_timeout":  "10s",
		"server.write_timeout": "10s",
		"server.idle_timeout":  "120s",
		"server.body_limit":    "4MB",
		"server.prefork":       false,

		"server.cors.allow_origins":     []string{"*"},
		"server.cors.allow_methods":     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		"server.cors.allow_headers":     []string{"*"},
		"server.cors.allow_credentials": true,
		"server.cors.max_age":           86400,

		"server.rate_limit.enabled":    true,
		"server.rate_limit.max":        100,
		"server.rate_limit.expiration": "1m",

		"database.host":              "localhost",
		"database.port":              "5432",
		"database.username":          "pantrypal",
		"database.database":          "pantrypal",
		"database.ssl_mode":          "disable",
		"database.max_idle_conns":    10,
		"database.max_open_conns":    100,
		"database.conn_max_lifetime": "1h",
		"database.log_level":         "warn",

		"redis.host":           "localhost",
		"redis.port":           "6379",
		"redis.database":       0,
		"redis.pool_size":      10,
		"redis.min_idle_conns": 5,
		"redis.max_retries":    3,

		"redis.idempotency.processing_ttl": "30s",
		"redis.idempotency.response_ttl":   "24h",
		"redis.idempotency.key_prefix":     "idempotency",

		"auth.jwt.access_token_expiration":  "15m",
		"auth.jwt.refresh_token_expiration": "7d",
		"auth.jwt.issuer":                   "pantry-pal",

		"logger.level":       "info",
		"logger.format":      "json",
		"logger.time_format": "2006-01-02T15:04:05Z07:00",
	}

	for key, value := range defaults {
		if !k.Exists(key) {
			k.Set(key, value)
		}
	}
}

// validateConfig validates required configuration fields
func validateConfig(config *Config) error {
	if config.Auth.JWT.Secret == "" {
		return fmt.Errorf("auth.jwt.secret is required")
	}

	if config.Database.Password == "" {
		return fmt.Errorf("database.password is required")
	}

	return nil
}
