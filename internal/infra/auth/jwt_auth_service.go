package auth

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/config"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// JWTAuthService implements the AuthService interface using JWT
type JWTAuthService struct {
	config                 config.JWTConfig
	userRepo               domain.UserRepository
	refreshTokenRepo       domain.RefreshTokenRepository
	accessTokenExpiration  time.Duration
	refreshTokenExpiration time.Duration
}

// NewJWTAuthService creates a new JWT authentication service
func NewJWTAuthService(
	cfg config.JWTConfig,
	userRepo domain.UserRepository,
	refreshTokenRepo domain.RefreshTokenRepository,
) (*JWTAuthService, error) {
	accessTokenExp, err := time.ParseDuration(cfg.AccessTokenExpiration)
	if err != nil {
		return nil, fmt.Errorf("invalid access token expiration: %w", err)
	}

	refreshTokenExp, err := time.ParseDuration(cfg.RefreshTokenExpiration)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token expiration: %w", err)
	}

	return &JWTAuthService{
		config:                 cfg,
		userRepo:               userRepo,
		refreshTokenRepo:       refreshTokenRepo,
		accessTokenExpiration:  accessTokenExp,
		refreshTokenExpiration: refreshTokenExp,
	}, nil
}

// GenerateTokens generates access and refresh tokens for a user
func (s *JWTAuthService) GenerateTokens(userID uuid.UUID) (*domain.TokenPair, error) {
	// Get user for email
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeInternalError, "failed to get user")
	}

	// Generate access token
	accessToken, accessExpiresAt, err := s.generateAccessToken(userID, user.Email)
	if err != nil {
		return nil, err
	}

	// Generate refresh token
	refreshToken, _, refreshExpiresAt, err := s.generateRefreshToken(userID)
	if err != nil {
		return nil, err
	}

	// Hash and store refresh token
	tokenHash, err := s.HashPassword(refreshToken)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeInternalError, "failed to hash refresh token")
	}

	refreshTokenEntity := domain.NewRefreshToken(userID, tokenHash, refreshExpiresAt)
	if err := s.refreshTokenRepo.Create(refreshTokenEntity); err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeInternalError, "failed to store refresh token")
	}

	return &domain.TokenPair{
		AccessToken:           accessToken,
		RefreshToken:          refreshToken,
		AccessTokenExpiresAt:  accessExpiresAt,
		RefreshTokenExpiresAt: refreshExpiresAt,
		TokenType:             "Bearer",
	}, nil
}

// ValidateAccessToken validates an access token and returns the user ID
func (s *JWTAuthService) ValidateAccessToken(tokenString string) (uuid.UUID, error) {
	claims, err := s.validateToken(tokenString)
	if err != nil {
		return uuid.Nil, err
	}

	if claims.Type != "access" {
		return uuid.Nil, errors.New(errors.ErrCodeTokenInvalid, "invalid token type")
	}

	return claims.UserID, nil
}

// ValidateRefreshToken validates a refresh token and returns the user ID
func (s *JWTAuthService) ValidateRefreshToken(tokenString string) (uuid.UUID, error) {
	claims, err := s.validateToken(tokenString)
	if err != nil {
		return uuid.Nil, err
	}

	if claims.Type != "refresh" {
		return uuid.Nil, errors.New(errors.ErrCodeTokenInvalid, "invalid token type")
	}

	// Check if token exists and is not revoked
	tokenHash, err := s.HashPassword(tokenString)
	if err != nil {
		return uuid.Nil, errors.Wrap(err, errors.ErrCodeInternalError, "failed to hash token")
	}

	refreshToken, err := s.refreshTokenRepo.GetByTokenHash(tokenHash)
	if err != nil {
		return uuid.Nil, errors.New(errors.ErrCodeTokenInvalid, "token not found")
	}

	if !refreshToken.IsValid() {
		return uuid.Nil, errors.New(errors.ErrCodeTokenExpired, "token is revoked or expired")
	}

	return claims.UserID, nil
}

// RefreshTokens generates new tokens using a valid refresh token
func (s *JWTAuthService) RefreshTokens(refreshTokenString string) (*domain.TokenPair, error) {
	// Validate refresh token
	userID, err := s.ValidateRefreshToken(refreshTokenString)
	if err != nil {
		return nil, err
	}

	// Revoke old refresh token
	if err := s.RevokeRefreshToken(refreshTokenString); err != nil {
		return nil, err
	}

	// Generate new token pair
	return s.GenerateTokens(userID)
}

// RevokeRefreshToken revokes a refresh token
func (s *JWTAuthService) RevokeRefreshToken(tokenString string) error {
	tokenHash, err := s.HashPassword(tokenString)
	if err != nil {
		return errors.Wrap(err, errors.ErrCodeInternalError, "failed to hash token")
	}

	return s.refreshTokenRepo.RevokeByTokenHash(tokenHash)
}

// RevokeAllUserTokens revokes all refresh tokens for a user
func (s *JWTAuthService) RevokeAllUserTokens(userID uuid.UUID) error {
	return s.refreshTokenRepo.RevokeByUserID(userID)
}

// HashPassword hashes a password using bcrypt
func (s *JWTAuthService) HashPassword(password string) (string, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", errors.Wrap(err, errors.ErrCodeInternalError, "failed to hash password")
	}
	return string(hash), nil
}

// VerifyPassword verifies a password against its hash
func (s *JWTAuthService) VerifyPassword(password, hash string) error {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	if err != nil {
		return errors.New(errors.ErrCodeInvalidCredentials, "invalid password")
	}
	return nil
}

// generateAccessToken generates an access token
func (s *JWTAuthService) generateAccessToken(userID uuid.UUID, email string) (string, time.Time, error) {
	now := time.Now()
	expiresAt := now.Add(s.accessTokenExpiration)

	claims := &domain.JWTClaims{
		UserID:    userID,
		Email:     email,
		Type:      "access",
		Subject:   userID.String(),
		ExpiresAt: expiresAt,
		IssuedAt:  now,
		NotBefore: now,
		Issuer:    s.config.Issuer,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"user_id": claims.UserID.String(),
		"email":   claims.Email,
		"type":    claims.Type,
		"sub":     claims.Subject,
		"exp":     claims.ExpiresAt.Unix(),
		"iat":     claims.IssuedAt.Unix(),
		"nbf":     claims.NotBefore.Unix(),
		"iss":     claims.Issuer,
	})

	tokenString, err := token.SignedString([]byte(s.config.Secret))
	if err != nil {
		return "", time.Time{}, errors.Wrap(err, errors.ErrCodeInternalError, "failed to sign token")
	}

	return tokenString, expiresAt, nil
}

// generateRefreshToken generates a refresh token
func (s *JWTAuthService) generateRefreshToken(userID uuid.UUID) (string, string, time.Time, error) {
	now := time.Now()
	expiresAt := now.Add(s.refreshTokenExpiration)
	jti := uuid.New().String()

	claims := &domain.JWTClaims{
		UserID:    userID,
		Type:      "refresh",
		Subject:   userID.String(),
		ExpiresAt: expiresAt,
		IssuedAt:  now,
		NotBefore: now,
		Issuer:    s.config.Issuer,
		JTI:       jti,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"user_id": claims.UserID.String(),
		"type":    claims.Type,
		"sub":     claims.Subject,
		"exp":     claims.ExpiresAt.Unix(),
		"iat":     claims.IssuedAt.Unix(),
		"nbf":     claims.NotBefore.Unix(),
		"iss":     claims.Issuer,
		"jti":     claims.JTI,
	})

	tokenString, err := token.SignedString([]byte(s.config.Secret))
	if err != nil {
		return "", "", time.Time{}, errors.Wrap(err, errors.ErrCodeInternalError, "failed to sign token")
	}

	return tokenString, jti, expiresAt, nil
}

// validateToken validates a JWT token and returns claims
func (s *JWTAuthService) validateToken(tokenString string) (*domain.JWTClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.config.Secret), nil
	})

	if err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeTokenInvalid, "failed to parse token")
	}

	if !token.Valid {
		return nil, errors.New(errors.ErrCodeTokenInvalid, "invalid token")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New(errors.ErrCodeTokenInvalid, "invalid token claims")
	}

	// Extract claims
	userIDStr, ok := claims["user_id"].(string)
	if !ok {
		return nil, errors.New(errors.ErrCodeTokenInvalid, "invalid user_id claim")
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, errors.New(errors.ErrCodeTokenInvalid, "invalid user_id format")
	}

	tokenType, ok := claims["type"].(string)
	if !ok {
		return nil, errors.New(errors.ErrCodeTokenInvalid, "invalid type claim")
	}

	exp, ok := claims["exp"].(float64)
	if !ok {
		return nil, errors.New(errors.ErrCodeTokenInvalid, "invalid exp claim")
	}

	expiresAt := time.Unix(int64(exp), 0)
	if time.Now().After(expiresAt) {
		return nil, errors.New(errors.ErrCodeTokenExpired, "token has expired")
	}

	jwtClaims := &domain.JWTClaims{
		UserID:    userID,
		Type:      tokenType,
		ExpiresAt: expiresAt,
	}

	if email, ok := claims["email"].(string); ok {
		jwtClaims.Email = email
	}

	if jti, ok := claims["jti"].(string); ok {
		jwtClaims.JTI = jti
	}

	return jwtClaims, nil
}
