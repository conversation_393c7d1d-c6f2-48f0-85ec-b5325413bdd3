package web

import (
	"context"
	"fmt"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/recover"
	fiberSwagger "github.com/swaggo/fiber-swagger"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/core/usecases"
	"github.com/wongpinter/pantry-pal/internal/infra/auth"
	"github.com/wongpinter/pantry-pal/internal/infra/config"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
	"github.com/wongpinter/pantry-pal/internal/infra/persistence/postgres"
	"github.com/wongpinter/pantry-pal/internal/infra/web/handler"
	"github.com/wongpinter/pantry-pal/internal/infra/web/middleware"
)

// Server represents the HTTP server
type Server struct {
	app    *fiber.App
	config *config.Config
	logger *logger.Logger
	db     *gorm.DB
}

// NewServer creates a new HTTP server
func NewServer(cfg *config.Config, log *logger.Logger) (*Server, error) {
	// Initialize database
	db, err := postgres.NewGormDB(cfg.Database)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize database: %w", err)
	}

	// Create Fiber app
	app := fiber.New(fiber.Config{
		ReadTimeout:  parseDuration(cfg.Server.ReadTimeout, 10*time.Second),
		WriteTimeout: parseDuration(cfg.Server.WriteTimeout, 10*time.Second),
		IdleTimeout:  parseDuration(cfg.Server.IdleTimeout, 120*time.Second),
		BodyLimit:    parseBodyLimit(cfg.Server.BodyLimit, 4*1024*1024), // 4MB default
		Prefork:      cfg.Server.Prefork,
		ErrorHandler: globalErrorHandler(log),
	})

	server := &Server{
		app:    app,
		config: cfg,
		logger: log,
		db:     db,
	}

	// Setup middleware
	server.setupMiddleware()

	// Setup routes
	server.setupRoutes()

	return server, nil
}

// Start starts the HTTP server
func (s *Server) Start() error {
	address := fmt.Sprintf("%s:%s", s.config.Server.Host, s.config.Server.Port)
	s.logger.Info().Str("address", address).Msg("Starting HTTP server")

	return s.app.Listen(address)
}

// Shutdown gracefully shuts down the server
func (s *Server) Shutdown(ctx context.Context) error {
	s.logger.Info().Msg("Shutting down HTTP server")

	// Shutdown Fiber app
	if err := s.app.ShutdownWithContext(ctx); err != nil {
		s.logger.Error().Err(err).Msg("Error shutting down HTTP server")
	}

	// Close database connection
	if err := postgres.CloseDB(s.db); err != nil {
		s.logger.Error().Err(err).Msg("Error closing database connection")
	}

	return nil
}

// setupMiddleware configures middleware
func (s *Server) setupMiddleware() {
	// Recovery middleware
	s.app.Use(recover.New(recover.Config{
		EnableStackTrace: s.config.App.Environment == "development",
	}))

	// Request ID middleware
	s.app.Use(middleware.RequestID())

	// Logger middleware
	s.app.Use(middleware.Logger(middleware.LoggerConfig{
		Logger: s.logger,
		SkipPaths: []string{
			"/health",
			"/metrics",
		},
		SkipSuccessfulRequests: false,
	}))

	// CORS middleware
	s.app.Use(cors.New(cors.Config{
		AllowOrigins:     joinStrings(s.config.Server.CORS.AllowOrigins, ","),
		AllowMethods:     joinStrings(s.config.Server.CORS.AllowMethods, ","),
		AllowHeaders:     joinStrings(s.config.Server.CORS.AllowHeaders, ","),
		AllowCredentials: s.config.Server.CORS.AllowCredentials,
		ExposeHeaders:    joinStrings(s.config.Server.CORS.ExposeHeaders, ","),
		MaxAge:           s.config.Server.CORS.MaxAge,
	}))
}

// setupRoutes configures API routes
func (s *Server) setupRoutes() {
	// Health check endpoint
	s.app.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status":    "ok",
			"timestamp": time.Now(),
			"version":   s.config.App.Version,
		})
	})

	// Swagger documentation
	s.app.Get("/docs/*", fiberSwagger.WrapHandler)

	// API routes
	api := s.app.Group("/api/v1")

	// Initialize repositories
	userRepo := postgres.NewUserRepository(s.db)
	refreshTokenRepo := postgres.NewRefreshTokenRepository(s.db)
	pantryRepo := postgres.NewPantryRepository(s.db)
	membershipRepo := postgres.NewPantryMembershipRepository(s.db)
	locationRepo := postgres.NewPantryLocationRepository(s.db)
	inventoryRepo := postgres.NewInventoryItemRepository(s.db)

	// Product catalog repositories
	categoryRepo := postgres.NewCategoryRepository(s.db)
	unitRepo := postgres.NewUnitOfMeasureRepository(s.db)
	productRepo := postgres.NewProductRepository(s.db)
	variantRepo := postgres.NewProductVariantRepository(s.db)

	// Initialize auth service
	authService, err := auth.NewJWTAuthService(s.config.Auth.JWT, userRepo, refreshTokenRepo)
	if err != nil {
		s.logger.Fatal().Err(err).Msg("Failed to initialize auth service")
	}

	// Initialize authorization service
	authzService := auth.NewPantryAuthorizationService(pantryRepo, membershipRepo)

	// Initialize use cases
	loggerAdapter := &LoggerAdapter{logger: s.logger}
	eventDispatcher := usecases.NewSimpleEventDispatcher(loggerAdapter)
	inventoryUsecase := usecases.NewInventoryUsecase(
		inventoryRepo,
		pantryRepo,
		variantRepo,
		unitRepo,
		locationRepo,
		authzService,
		eventDispatcher,
		loggerAdapter,
	)

	// TODO: Create alert configuration repository when we implement persistence
	// For now, we'll create a placeholder that will be implemented later
	// alertConfigRepo := postgres.NewAlertConfigurationRepository(s.db)
	// notificationService := services.NewNotificationService(notificationRepo, loggerAdapter)

	// Create expiration use case with placeholder dependencies
	// This will be fully implemented when we add the missing repositories
	var expirationUsecase *usecases.ExpirationUsecase
	// expirationUsecase = usecases.NewExpirationUsecase(
	//     inventoryRepo,
	//     pantryRepo,
	//     variantRepo,
	//     unitRepo,
	//     alertConfigRepo,
	//     authzService,
	//     notificationService,
	//     loggerAdapter,
	// )

	// Auth routes (public)
	authGroup := api.Group("/auth")
	s.setupAuthRoutes(authGroup, authService, userRepo)

	// Protected routes
	protected := api.Group("")
	protected.Use(middleware.JWTAuth(middleware.JWTAuthConfig{
		AuthService: authService,
	}))

	// User routes
	userGroup := protected.Group("/users")
	s.setupUserRoutes(userGroup, userRepo)

	// Pantry routes
	pantryGroup := protected.Group("/pantries")
	s.setupPantryRoutes(pantryGroup, pantryRepo, membershipRepo, locationRepo, userRepo, authzService, inventoryUsecase, expirationUsecase)

	// Product catalog routes
	catalogGroup := protected.Group("/catalog")
	s.setupProductCatalogRoutes(catalogGroup, categoryRepo, unitRepo, productRepo, variantRepo)
}

// setupAuthRoutes configures authentication routes
func (s *Server) setupAuthRoutes(group fiber.Router, authService domain.AuthService, userRepo domain.UserRepository) {
	authHandler := handler.NewAuthHandler(authService, userRepo, s.logger)

	group.Post("/register", authHandler.Register)
	group.Post("/login", authHandler.Login)
	group.Post("/refresh", authHandler.RefreshToken)
	group.Post("/logout", authHandler.Logout)
}

// setupUserRoutes configures user routes
func (s *Server) setupUserRoutes(group fiber.Router, userRepo domain.UserRepository) {
	userHandler := handler.NewUserHandler(userRepo, s.logger)

	group.Get("/profile", userHandler.GetProfile)
	group.Put("/profile", userHandler.UpdateProfile)
	group.Post("/change-password", userHandler.ChangePassword)
}

// setupPantryRoutes configures pantry routes
func (s *Server) setupPantryRoutes(
	group fiber.Router,
	pantryRepo domain.PantryRepository,
	membershipRepo domain.PantryMembershipRepository,
	locationRepo domain.PantryLocationRepository,
	userRepo domain.UserRepository,
	authzService domain.PantryAuthorizationService,
	inventoryUsecase *usecases.InventoryUsecase,
	expirationUsecase *usecases.ExpirationUsecase,
) {
	pantryHandler := handler.NewPantryHandler(pantryRepo, membershipRepo, locationRepo, userRepo, authzService, s.logger)
	membershipHandler := handler.NewPantryMembershipHandler(pantryRepo, membershipRepo, userRepo, authzService, s.logger)
	locationHandler := handler.NewPantryLocationHandler(pantryRepo, locationRepo, authzService, s.logger)
	inventoryHandler := handler.NewInventoryHandler(inventoryUsecase, s.logger)

	// Only create expiration handler if use case is available
	var expirationHandler *handler.ExpirationHandler
	if expirationUsecase != nil {
		expirationHandler = handler.NewExpirationHandler(expirationUsecase, s.logger)
	}

	// Pantry management
	group.Post("/", pantryHandler.CreatePantry)
	group.Get("/", pantryHandler.GetPantries)
	group.Get("/:pantryId", pantryHandler.GetPantry)
	group.Put("/:pantryId", pantryHandler.UpdatePantry)
	group.Delete("/:pantryId", pantryHandler.DeletePantry)
	group.Post("/:pantryId/transfer-ownership", pantryHandler.TransferOwnership)

	// Member management
	group.Post("/:pantryId/members", membershipHandler.InviteMember)
	group.Get("/:pantryId/members", membershipHandler.GetMembers)
	group.Put("/:pantryId/members/:memberId", membershipHandler.UpdateMemberRole)
	group.Delete("/:pantryId/members/:memberId", membershipHandler.RemoveMember)
	group.Post("/:pantryId/leave", membershipHandler.LeavePantry)

	// Invitation management
	group.Get("/invitations", membershipHandler.GetPendingInvitations)
	group.Post("/invitations/accept", membershipHandler.AcceptInvitation)
	group.Post("/invitations/reject", membershipHandler.RejectInvitation)

	// Location management
	group.Post("/:pantryId/locations", locationHandler.CreateLocation)
	group.Get("/:pantryId/locations", locationHandler.GetLocations)
	group.Get("/:pantryId/locations/:locationId", locationHandler.GetLocation)
	group.Put("/:pantryId/locations/:locationId", locationHandler.UpdateLocation)
	group.Delete("/:pantryId/locations/:locationId", locationHandler.DeleteLocation)

	// Inventory management
	group.Post("/:pantryId/inventory", inventoryHandler.CreateInventoryItem)
	group.Get("/:pantryId/inventory", inventoryHandler.GetPantryInventory)
	group.Get("/:pantryId/inventory/:itemId", inventoryHandler.GetInventoryItem)
	group.Put("/:pantryId/inventory/:itemId", inventoryHandler.UpdateInventoryItem)
	group.Post("/:pantryId/inventory/:itemId/consume", inventoryHandler.ConsumeInventoryItem)

	// Bulk inventory operations
	group.Post("/:pantryId/inventory/bulk", inventoryHandler.BulkCreateInventoryItems)
	group.Put("/inventory/bulk", inventoryHandler.BulkUpdateInventoryItems)
	group.Post("/inventory/bulk/consume", inventoryHandler.BulkConsumeInventoryItems)
	group.Delete("/inventory/bulk", inventoryHandler.BulkDeleteInventoryItems)

	// Recipe ingredient consumption
	group.Post("/:pantryId/inventory/recipe/consume", inventoryHandler.ConsumeRecipeIngredients)

	// Shopping list generation
	group.Post("/:pantryId/inventory/shopping-list", inventoryHandler.GenerateShoppingList)

	// Expiration tracking and alerts (only if handler is available)
	if expirationHandler != nil {
		group.Post("/:pantryId/expiration/track", expirationHandler.TrackExpiringItems)
		group.Post("/:pantryId/expiration/alerts", expirationHandler.ConfigureAlerts)
		group.Get("/:pantryId/expiration/alerts", expirationHandler.GetAlertConfiguration)
		group.Post("/expiration/alerts/global", expirationHandler.ConfigureAlerts)
		group.Get("/expiration/alerts/global", expirationHandler.GetAlertConfiguration)
	}
}

// setupProductCatalogRoutes configures product catalog routes
func (s *Server) setupProductCatalogRoutes(
	group fiber.Router,
	categoryRepo domain.CategoryRepository,
	unitRepo domain.UnitOfMeasureRepository,
	productRepo domain.ProductRepository,
	variantRepo domain.ProductVariantRepository,
) {
	categoryHandler := handler.NewCategoryHandler(categoryRepo, s.logger)
	unitHandler := handler.NewUnitOfMeasureHandler(unitRepo, s.logger)

	// Category routes
	categoryGroup := group.Group("/categories")
	categoryGroup.Post("/", categoryHandler.CreateCategory)
	categoryGroup.Get("/", categoryHandler.GetCategories)
	categoryGroup.Get("/:categoryId", categoryHandler.GetCategory)
	categoryGroup.Get("/:categoryId/subcategories", categoryHandler.GetSubCategories)
	categoryGroup.Put("/:categoryId", categoryHandler.UpdateCategory)
	categoryGroup.Post("/:categoryId/move", categoryHandler.MoveCategory)
	categoryGroup.Delete("/:categoryId", categoryHandler.DeleteCategory)

	// Unit of measure routes
	unitGroup := group.Group("/units")
	unitGroup.Post("/", unitHandler.CreateUnit)
	unitGroup.Post("/derived", unitHandler.CreateDerivedUnit)
	unitGroup.Get("/", unitHandler.GetUnits)
	unitGroup.Get("/:unitId", unitHandler.GetUnit)
	unitGroup.Get("/:unitId/derived", unitHandler.GetDerivedUnits)
	unitGroup.Put("/:unitId", unitHandler.UpdateUnit)
	unitGroup.Put("/:unitId/conversion-factor", unitHandler.UpdateConversionFactor)
	unitGroup.Delete("/:unitId", unitHandler.DeleteUnit)

	// Product and variant handlers
	productHandler := handler.NewProductHandler(productRepo, categoryRepo, s.logger)
	variantHandler := handler.NewProductVariantHandler(variantRepo, productRepo, unitRepo, s.logger)

	// Product routes
	productGroup := group.Group("/products")
	productGroup.Post("/", productHandler.CreateProduct)
	productGroup.Get("/", productHandler.GetProducts)
	productGroup.Get("/:productId", productHandler.GetProduct)
	productGroup.Put("/:productId", productHandler.UpdateProduct)
	productGroup.Delete("/:productId", productHandler.DeleteProduct)

	// Product variant routes
	productGroup.Post("/:productId/variants", variantHandler.CreateProductVariant)
	productGroup.Get("/:productId/variants", variantHandler.GetProductVariants)

	// Variant routes
	variantGroup := group.Group("/variants")
	variantGroup.Get("/", variantHandler.SearchProductVariants)
	variantGroup.Get("/:variantId", variantHandler.GetProductVariant)
	variantGroup.Put("/:variantId", variantHandler.UpdateProductVariant)
	variantGroup.Put("/:variantId/barcode", variantHandler.UpdateProductVariantBarcode)
	variantGroup.Delete("/:variantId", variantHandler.DeleteProductVariant)

	// Barcode lookup route
	group.Get("/barcode/:barcode", variantHandler.GetProductVariantByBarcode)
}

// globalErrorHandler handles global errors
func globalErrorHandler(log *logger.Logger) fiber.ErrorHandler {
	return func(c *fiber.Ctx, err error) error {
		// Log the error
		log.LogError(err, "Unhandled error in request", map[string]interface{}{
			"method": c.Method(),
			"path":   c.Path(),
		})

		// Return error response
		return handler.ErrorResponse(c, err)
	}
}

// Helper functions

func parseDuration(s string, defaultDuration time.Duration) time.Duration {
	if s == "" {
		return defaultDuration
	}

	duration, err := time.ParseDuration(s)
	if err != nil {
		return defaultDuration
	}

	return duration
}

func parseBodyLimit(s string, defaultLimit int) int {
	if s == "" {
		return defaultLimit
	}

	// Simple parsing for common formats like "4MB", "1GB", etc.
	// For now, return default - can be enhanced later
	return defaultLimit
}

func joinStrings(slice []string, sep string) string {
	if len(slice) == 0 {
		return ""
	}

	result := slice[0]
	for i := 1; i < len(slice); i++ {
		result += sep + slice[i]
	}

	return result
}

// LoggerAdapter adapts the logger.Logger to the usecases.Logger interface
type LoggerAdapter struct {
	logger *logger.Logger
}

func (l *LoggerAdapter) Info(msg string, fields ...map[string]interface{}) {
	event := l.logger.Info()
	if len(fields) > 0 {
		for key, value := range fields[0] {
			event = event.Interface(key, value)
		}
	}
	event.Msg(msg)
}

func (l *LoggerAdapter) Error(msg string, err error, fields ...map[string]interface{}) {
	event := l.logger.Error().Err(err)
	if len(fields) > 0 {
		for key, value := range fields[0] {
			event = event.Interface(key, value)
		}
	}
	event.Msg(msg)
}

func (l *LoggerAdapter) Debug(msg string, fields ...map[string]interface{}) {
	event := l.logger.Debug()
	if len(fields) > 0 {
		for key, value := range fields[0] {
			event = event.Interface(key, value)
		}
	}
	event.Msg(msg)
}

func (l *LoggerAdapter) LogBusinessEvent(eventType, entityID string, data map[string]interface{}) {
	l.logger.LogBusinessEvent(eventType, entityID, data)
}
