package middleware

import (
	"encoding/json"
	"strings"

	"github.com/gofiber/fiber/v2"

	"github.com/wongpinter/pantry-pal/internal/infra/logger"
	"github.com/wongpinter/pantry-pal/internal/infra/persistence/redis"
	"github.com/wongpinter/pantry-pal/internal/infra/web/handler"
)

const (
	// IdempotencyKeyHeader is the header name for idempotency key
	IdempotencyKeyHeader = "Idempotency-Key"

	// ContextKeyIdempotencyKey is the context key for storing idempotency key
	ContextKeyIdempotencyKey = "idempotency_key"

	// ContextKeyIdempotencyService is the context key for storing idempotency service
	ContextKeyIdempotencyService = "idempotency_service"
)

// IdempotencyConfig holds configuration for idempotency middleware
type IdempotencyConfig struct {
	// RequiredMethods specifies which HTTP methods require idempotency keys
	RequiredMethods []string

	// OptionalMethods specifies which HTTP methods support optional idempotency keys
	OptionalMethods []string

	// SkipPaths specifies paths that should skip idempotency checks
	SkipPaths []string

	// SkipPathPrefixes specifies path prefixes that should skip idempotency checks
	SkipPathPrefixes []string
}

// DefaultIdempotencyConfig returns the default configuration
func DefaultIdempotencyConfig() IdempotencyConfig {
	return IdempotencyConfig{
		RequiredMethods:  []string{"POST", "PUT", "PATCH"},
		OptionalMethods:  []string{}, // Can add DELETE if needed
		SkipPaths:        []string{"/health", "/metrics", "/api/v1/auth/login", "/api/v1/auth/refresh"},
		SkipPathPrefixes: []string{"/docs", "/swagger"},
	}
}

// IdempotencyMiddleware creates a new idempotency middleware
func IdempotencyMiddleware(idempotencyService *redis.IdempotencyService, log *logger.Logger, config ...IdempotencyConfig) fiber.Handler {
	cfg := DefaultIdempotencyConfig()
	if len(config) > 0 {
		cfg = config[0]
	}

	return func(c *fiber.Ctx) error {
		// Store idempotency service in context for use in handlers
		c.Locals(ContextKeyIdempotencyService, idempotencyService)

		// Check if path should be skipped
		if shouldSkipPath(c.Path(), cfg) {
			return c.Next()
		}

		method := c.Method()
		idempotencyKey := c.Get(IdempotencyKeyHeader)

		// Check if method requires or supports idempotency
		requiresKey := contains(cfg.RequiredMethods, method)
		supportsKey := requiresKey || contains(cfg.OptionalMethods, method)

		if !supportsKey {
			// Method doesn't support idempotency, continue normally
			return c.Next()
		}

		if requiresKey && idempotencyKey == "" {
			log.LogWarn("Missing required idempotency key", map[string]interface{}{
				"method":     method,
				"path":       c.Path(),
				"request_id": c.Locals("request_id"),
			})
			return handler.BadRequest(c, "Idempotency-Key header is required for this operation", nil)
		}

		if idempotencyKey == "" {
			// Optional idempotency, continue normally
			return c.Next()
		}

		// Store idempotency key in context
		c.Locals(ContextKeyIdempotencyKey, idempotencyKey)

		// Check idempotency status
		result, err := idempotencyService.CheckIdempotency(c.Context(), idempotencyKey)
		if err != nil {
			log.LogError(err, "Failed to check idempotency", map[string]interface{}{
				"idempotency_key": idempotencyKey,
				"method":          method,
				"path":            c.Path(),
				"request_id":      c.Locals("request_id"),
			})
			// Continue with request if idempotency check fails (graceful degradation)
			return c.Next()
		}

		// If response is already cached, return it
		if result.HasResponse {
			log.LogInfo("Returning cached idempotent response", map[string]interface{}{
				"idempotency_key": idempotencyKey,
				"status_code":     result.Response.StatusCode,
				"cached_at":       result.Response.CachedAt,
				"request_id":      c.Locals("request_id"),
			})

			// Set cached headers if any
			if result.Response.Headers != nil {
				for key, value := range result.Response.Headers {
					c.Set(key, value)
				}
			}

			// Return cached response
			c.Status(result.Response.StatusCode)
			return c.JSON(result.Response.Body)
		}

		// If request is currently being processed, return conflict
		if result.IsProcessing {
			log.LogWarn("Request with idempotency key is already being processed", map[string]interface{}{
				"idempotency_key": idempotencyKey,
				"method":          method,
				"path":            c.Path(),
				"request_id":      c.Locals("request_id"),
			})
			return handler.Conflict(c, "Request with this idempotency key is already being processed", map[string]interface{}{
				"idempotency_key": idempotencyKey,
				"retry_after":     "30", // Suggest retry after 30 seconds
			})
		}

		// Mark request as processing
		if err := idempotencyService.StartProcessing(c.Context(), idempotencyKey); err != nil {
			log.LogError(err, "Failed to mark request as processing", map[string]interface{}{
				"idempotency_key": idempotencyKey,
				"method":          method,
				"path":            c.Path(),
				"request_id":      c.Locals("request_id"),
			})
			// Continue with request if marking fails (graceful degradation)
			return c.Next()
		}

		// Continue with the request
		err = c.Next()

		// After request processing, handle idempotency caching
		statusCode := c.Response().StatusCode()

		if err != nil {
			// Request failed, clear processing status
			if clearErr := idempotencyService.ClearProcessing(c.Context(), idempotencyKey); clearErr != nil {
				log.LogError(clearErr, "Failed to clear processing status after error", map[string]interface{}{
					"idempotency_key": idempotencyKey,
					"original_error":  err.Error(),
					"request_id":      c.Locals("request_id"),
				})
			}
			return err
		}

		// Get response body for caching (simplified approach)
		responseBody := string(c.Response().Body())
		var parsedBody interface{}
		if len(responseBody) > 0 {
			if jsonErr := json.Unmarshal([]byte(responseBody), &parsedBody); jsonErr != nil {
				// If JSON parsing fails, store as string
				parsedBody = responseBody
			}
		}

		// Collect relevant headers for caching
		headers := make(map[string]string)
		c.Response().Header.VisitAll(func(key, value []byte) {
			keyStr := string(key)
			// Only cache certain headers
			if shouldCacheHeader(keyStr) {
				headers[keyStr] = string(value)
			}
		})

		// Cache the response or clear processing status
		if cacheErr := idempotencyService.CacheResponse(c.Context(), idempotencyKey, statusCode, parsedBody, headers); cacheErr != nil {
			log.LogError(cacheErr, "Failed to cache idempotent response", map[string]interface{}{
				"idempotency_key": idempotencyKey,
				"status_code":     statusCode,
				"request_id":      c.Locals("request_id"),
			})
		}

		return nil
	}
}

// GetIdempotencyKey retrieves the idempotency key from the context
func GetIdempotencyKey(c *fiber.Ctx) string {
	if key, ok := c.Locals(ContextKeyIdempotencyKey).(string); ok {
		return key
	}
	return ""
}

// GetIdempotencyService retrieves the idempotency service from the context
func GetIdempotencyService(c *fiber.Ctx) *redis.IdempotencyService {
	if service, ok := c.Locals(ContextKeyIdempotencyService).(*redis.IdempotencyService); ok {
		return service
	}
	return nil
}

// Helper functions

func shouldSkipPath(path string, config IdempotencyConfig) bool {
	// Check exact paths
	for _, skipPath := range config.SkipPaths {
		if path == skipPath {
			return true
		}
	}

	// Check path prefixes
	for _, prefix := range config.SkipPathPrefixes {
		if strings.HasPrefix(path, prefix) {
			return true
		}
	}

	return false
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func shouldCacheHeader(headerName string) bool {
	// List of headers that should be cached with the response
	cacheableHeaders := []string{
		"Content-Type",
		"Content-Language",
		"Cache-Control",
		"ETag",
		"Last-Modified",
		"Location",
	}

	headerLower := strings.ToLower(headerName)
	for _, cacheable := range cacheableHeaders {
		if strings.ToLower(cacheable) == headerLower {
			return true
		}
	}

	return false
}
