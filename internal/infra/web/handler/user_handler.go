package handler

import (
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// UserHandler handles user-related HTTP requests
type UserHandler struct {
	userRepo  domain.UserRepository
	logger    *logger.Logger
	validator *validator.Validate
}

// NewUserHandler creates a new user handler
func NewUserHandler(userRepo domain.UserRepository, log *logger.Logger) *UserHandler {
	return &UserHandler{
		userRepo:  userRepo,
		logger:    log,
		validator: validator.New(),
	}
}

// GetProfile retrieves the current user's profile
func (h *UserHandler) GetProfile(c *fiber.Ctx) error {
	// Get user ID from context
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	// Get user from database
	user, err := h.userRepo.GetByID(userID)
	if err != nil {
		h.logger.LogError(err, "Failed to get user profile", map[string]interface{}{
			"user_id": userIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Return user profile
	response := map[string]interface{}{
		"id":                  user.ID,
		"username":            user.Username,
		"email":               user.Email,
		"first_name":          user.FirstName,
		"last_name":           user.LastName,
		"profile_picture_url": user.ProfilePictureURL,
		"created_at":          user.CreatedAt,
		"updated_at":          user.UpdatedAt,
	}

	return SuccessResponse(c, response, "Profile retrieved successfully")
}

// UpdateProfile updates the current user's profile
func (h *UserHandler) UpdateProfile(c *fiber.Ctx) error {
	// Get user ID from context
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	var req domain.UpdateProfileRequest

	// Parse request body
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Validate request
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	// Get current user
	user, err := h.userRepo.GetByID(userID)
	if err != nil {
		h.logger.LogError(err, "Failed to get user for profile update", map[string]interface{}{
			"user_id": userIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Update profile
	user.UpdateProfile(req.FirstName, req.LastName, req.ProfilePictureURL)

	// Save changes
	if err := h.userRepo.Update(user); err != nil {
		h.logger.LogError(err, "Failed to update user profile", map[string]interface{}{
			"user_id": userIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Log profile update
	h.logger.LogBusinessEvent("user.profile_updated", userIDStr, map[string]interface{}{
		"email": user.Email,
	})

	// Return updated profile
	response := map[string]interface{}{
		"id":                  user.ID,
		"username":            user.Username,
		"email":               user.Email,
		"first_name":          user.FirstName,
		"last_name":           user.LastName,
		"profile_picture_url": user.ProfilePictureURL,
		"updated_at":          user.UpdatedAt,
	}

	return SuccessResponse(c, response, "Profile updated successfully")
}

// ChangePassword changes the current user's password
func (h *UserHandler) ChangePassword(c *fiber.Ctx) error {
	// Get user ID from context
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	_, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	var req domain.ChangePasswordRequest

	// Parse request body
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Validate request
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	// For now, we'll return an error indicating this needs to be implemented
	// In a real implementation, we'd inject the auth service into this handler
	// to verify the current password and hash the new password
	return ErrorResponse(c, errors.New(errors.ErrCodeInternalError, "Password change not yet implemented"))
}

// Helper methods

func (h *UserHandler) parseValidationErrors(err error) map[string]string {
	validationErrors := make(map[string]string)

	if validationErr, ok := err.(validator.ValidationErrors); ok {
		for _, fieldErr := range validationErr {
			field := strings.ToLower(fieldErr.Field())
			switch fieldErr.Tag() {
			case "required":
				validationErrors[field] = "This field is required"
			case "email":
				validationErrors[field] = "Invalid email format"
			case "min":
				validationErrors[field] = "Value is too short"
			case "max":
				validationErrors[field] = "Value is too long"
			case "url":
				validationErrors[field] = "Invalid URL format"
			case "eqfield":
				validationErrors[field] = "Values do not match"
			default:
				validationErrors[field] = "Invalid value"
			}
		}
	}

	return validationErrors
}

// getUserIDFromContext extracts user ID from fiber context
func getUserIDFromContext(c *fiber.Ctx) (string, bool) {
	userID := c.Locals("user_id")
	if userID == nil {
		return "", false
	}

	userIDStr, ok := userID.(string)
	return userIDStr, ok
}
