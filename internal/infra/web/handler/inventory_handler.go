package handler

import (
	"net/http"

	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/core/usecases"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// InventoryHandler handles inventory-related HTTP requests
type InventoryHandler struct {
	inventoryUsecase *usecases.InventoryUsecase
}

// NewInventoryHandler creates a new inventory handler
func NewInventoryHandler(inventoryUsecase *usecases.InventoryUsecase) *InventoryHandler {
	return &InventoryHandler{
		inventoryUsecase: inventoryUsecase,
	}
}

// CreateInventoryItem creates a new inventory item
// @Summary Create inventory item
// @Description Create a new inventory item in a pantry
// @Tags inventory
// @Accept json
// @Produce json
// @Param pantry_id path string true "Pantry ID"
// @Param request body domain.CreateInventoryItemRequest true "Create inventory item request"
// @Success 201 {object} SuccessResponse{data=domain.InventoryItemResponse}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /pantries/{pantry_id}/inventory [post]
// @Security BearerAuth
func (h *InventoryHandler) CreateInventoryItem(c *gin.Context) {
	userID := GetUserIDFromContext(c)
	pantryID, err := uuid.Parse(c.Param("pantry_id"))
	if err != nil {
		RespondWithError(c, http.StatusBadRequest, errors.ErrInvalidInput, "Invalid pantry ID")
		return
	}

	var req domain.CreateInventoryItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		RespondWithError(c, http.StatusBadRequest, errors.ErrInvalidInput, "Invalid request body")
		return
	}

	item, err := h.inventoryUsecase.CreateInventoryItem(c.Request.Context(), userID, pantryID, &req)
	if err != nil {
		HandleUsecaseError(c, err)
		return
	}

	response := convertInventoryItemToResponse(item)
	RespondWithSuccess(c, http.StatusCreated, "Inventory item created successfully", response)
}

// GetInventoryItem retrieves an inventory item by ID
// @Summary Get inventory item
// @Description Get an inventory item by ID
// @Tags inventory
// @Produce json
// @Param pantry_id path string true "Pantry ID"
// @Param item_id path string true "Item ID"
// @Success 200 {object} SuccessResponse{data=domain.InventoryItemResponse}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /pantries/{pantry_id}/inventory/{item_id} [get]
// @Security BearerAuth
func (h *InventoryHandler) GetInventoryItem(c *gin.Context) {
	userID := GetUserIDFromContext(c)
	itemID, err := uuid.Parse(c.Param("item_id"))
	if err != nil {
		RespondWithError(c, http.StatusBadRequest, errors.ErrInvalidInput, "Invalid item ID")
		return
	}

	item, err := h.inventoryUsecase.GetInventoryItem(c.Request.Context(), userID, itemID)
	if err != nil {
		HandleUsecaseError(c, err)
		return
	}

	response := convertInventoryItemToResponse(item)
	RespondWithSuccess(c, http.StatusOK, "Inventory item retrieved successfully", response)
}

// GetPantryInventory retrieves inventory items for a pantry
// @Summary Get pantry inventory
// @Description Get inventory items for a pantry with pagination
// @Tags inventory
// @Produce json
// @Param pantry_id path string true "Pantry ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} SuccessResponse{data=PaginatedResponse{items=[]domain.InventoryItemResponse}}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /pantries/{pantry_id}/inventory [get]
// @Security BearerAuth
func (h *InventoryHandler) GetPantryInventory(c *gin.Context) {
	userID := GetUserIDFromContext(c)
	pantryID, err := uuid.Parse(c.Param("pantry_id"))
	if err != nil {
		RespondWithError(c, http.StatusBadRequest, errors.ErrInvalidInput, "Invalid pantry ID")
		return
	}

	// Parse pagination parameters
	page, limit := ParsePaginationParams(c)

	items, total, err := h.inventoryUsecase.GetPantryInventory(c.Request.Context(), userID, pantryID, page, limit)
	if err != nil {
		HandleUsecaseError(c, err)
		return
	}

	responses := make([]domain.InventoryItemResponse, len(items))
	for i, item := range items {
		responses[i] = *convertInventoryItemToResponse(item)
	}

	paginatedResponse := PaginatedResponse{
		Items:      responses,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	}

	RespondWithSuccess(c, http.StatusOK, "Pantry inventory retrieved successfully", paginatedResponse)
}

// UpdateInventoryItem updates an inventory item
// @Summary Update inventory item
// @Description Update an inventory item
// @Tags inventory
// @Accept json
// @Produce json
// @Param pantry_id path string true "Pantry ID"
// @Param item_id path string true "Item ID"
// @Param request body domain.UpdateInventoryItemRequest true "Update inventory item request"
// @Success 200 {object} SuccessResponse{data=domain.InventoryItemResponse}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /pantries/{pantry_id}/inventory/{item_id} [put]
// @Security BearerAuth
func (h *InventoryHandler) UpdateInventoryItem(c *gin.Context) {
	userID := GetUserIDFromContext(c)
	itemID, err := uuid.Parse(c.Param("item_id"))
	if err != nil {
		RespondWithError(c, http.StatusBadRequest, errors.ErrInvalidInput, "Invalid item ID")
		return
	}

	var req domain.UpdateInventoryItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		RespondWithError(c, http.StatusBadRequest, errors.ErrInvalidInput, "Invalid request body")
		return
	}

	item, err := h.inventoryUsecase.UpdateInventoryItem(c.Request.Context(), userID, itemID, &req)
	if err != nil {
		HandleUsecaseError(c, err)
		return
	}

	response := convertInventoryItemToResponse(item)
	RespondWithSuccess(c, http.StatusOK, "Inventory item updated successfully", response)
}

// ConsumeInventoryItem consumes quantity from an inventory item
// @Summary Consume inventory item
// @Description Consume a quantity from an inventory item
// @Tags inventory
// @Accept json
// @Produce json
// @Param pantry_id path string true "Pantry ID"
// @Param item_id path string true "Item ID"
// @Param request body domain.ConsumeInventoryRequest true "Consume inventory request"
// @Success 200 {object} SuccessResponse{data=domain.InventoryItemResponse}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /pantries/{pantry_id}/inventory/{item_id}/consume [post]
// @Security BearerAuth
func (h *InventoryHandler) ConsumeInventoryItem(c *gin.Context) {
	userID := GetUserIDFromContext(c)
	itemID, err := uuid.Parse(c.Param("item_id"))
	if err != nil {
		RespondWithError(c, http.StatusBadRequest, errors.ErrInvalidInput, "Invalid item ID")
		return
	}

	var req domain.ConsumeInventoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		RespondWithError(c, http.StatusBadRequest, errors.ErrInvalidInput, "Invalid request body")
		return
	}

	item, err := h.inventoryUsecase.ConsumeInventoryItem(c.Request.Context(), userID, itemID, &req)
	if err != nil {
		HandleUsecaseError(c, err)
		return
	}

	response := convertInventoryItemToResponse(item)
	RespondWithSuccess(c, http.StatusOK, "Inventory item consumed successfully", response)
}

// Helper functions

// convertInventoryItemToResponse converts a domain inventory item to response format
func convertInventoryItemToResponse(item *domain.InventoryItem) *domain.InventoryItemResponse {
	response := &domain.InventoryItemResponse{
		ID:               item.ID,
		PantryID:         item.PantryID,
		LocationID:       item.LocationID,
		ProductVariantID: item.ProductVariantID,
		Quantity:         item.Quantity,
		UnitOfMeasureID:  item.UnitOfMeasureID,
		PurchasePrice:    item.PurchasePrice,
		Notes:            item.Notes,
		Status:           item.Status,
		CreatedAt:        item.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:        item.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}

	// Convert dates to string pointers
	if item.PurchaseDate != nil {
		dateStr := item.PurchaseDate.Format("2006-01-02")
		response.PurchaseDate = &dateStr
	}

	if item.ExpirationDate != nil {
		dateStr := item.ExpirationDate.Format("2006-01-02")
		response.ExpirationDate = &dateStr
	}

	return response
}
