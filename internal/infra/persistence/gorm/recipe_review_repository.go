package gorm

import (
	"fmt"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
)

// RecipeReviewRepository implements the recipe review repository using GORM
type RecipeReviewRepository struct {
	db *gorm.DB
}

// NewRecipeReviewRepository creates a new recipe review repository
func NewRecipeReviewRepository(db *gorm.DB) domain.RecipeReviewRepository {
	return &RecipeReviewRepository{db: db}
}

// Create creates a new recipe review
func (r *RecipeReviewRepository) Create(review *domain.RecipeReview) error {
	return r.db.Create(review).Error
}

// GetByID retrieves a recipe review by ID
func (r *RecipeReviewRepository) GetByID(id uuid.UUID) (*domain.RecipeReview, error) {
	var review domain.RecipeReview
	err := r.db.Preload("User").Preload("Recipe").Where("id = ?", id).First(&review).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("recipe review not found")
		}
		return nil, err
	}
	return &review, nil
}

// GetByRecipeID retrieves reviews for a recipe with pagination
func (r *RecipeReviewRepository) GetByRecipeID(recipeID uuid.UUID, page, limit int) ([]*domain.RecipeReview, int64, error) {
	var reviews []*domain.RecipeReview
	var total int64

	// Count total
	if err := r.db.Model(&domain.RecipeReview{}).Where("recipe_id = ?", recipeID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (page - 1) * limit
	err := r.db.Where("recipe_id = ?", recipeID).
		Preload("User").
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&reviews).Error

	if err != nil {
		return nil, 0, err
	}

	return reviews, total, nil
}

// GetByUserID retrieves reviews by a user with pagination
func (r *RecipeReviewRepository) GetByUserID(userID uuid.UUID, page, limit int) ([]*domain.RecipeReview, int64, error) {
	var reviews []*domain.RecipeReview
	var total int64

	// Count total
	if err := r.db.Model(&domain.RecipeReview{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (page - 1) * limit
	err := r.db.Where("user_id = ?", userID).
		Preload("Recipe").
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&reviews).Error

	if err != nil {
		return nil, 0, err
	}

	return reviews, total, nil
}

// Update updates a recipe review
func (r *RecipeReviewRepository) Update(review *domain.RecipeReview) error {
	return r.db.Save(review).Error
}

// Delete deletes a recipe review
func (r *RecipeReviewRepository) Delete(id uuid.UUID) error {
	return r.db.Delete(&domain.RecipeReview{}, id).Error
}

// GetAverageRating calculates the average rating for a recipe
func (r *RecipeReviewRepository) GetAverageRating(recipeID uuid.UUID) (float64, error) {
	var result struct {
		Average float64
	}

	err := r.db.Model(&domain.RecipeReview{}).
		Select("AVG(rating) as average").
		Where("recipe_id = ?", recipeID).
		Scan(&result).Error

	if err != nil {
		return 0, err
	}

	return result.Average, nil
}

// GetRatingDistribution gets the distribution of ratings for a recipe
func (r *RecipeReviewRepository) GetRatingDistribution(recipeID uuid.UUID) (map[int]int, error) {
	var results []struct {
		Rating int
		Count  int
	}

	err := r.db.Model(&domain.RecipeReview{}).
		Select("rating, COUNT(*) as count").
		Where("recipe_id = ?", recipeID).
		Group("rating").
		Order("rating").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	distribution := make(map[int]int)
	for _, result := range results {
		distribution[result.Rating] = result.Count
	}

	return distribution, nil
}
