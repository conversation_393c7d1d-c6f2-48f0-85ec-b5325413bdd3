-- Create inventory_items table
CREATE TABLE inventory_items (
    item_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pantry_id UUID NOT NULL REFERENCES pantries(pantry_id) ON DELETE CASCADE,
    location_id UUID REFERENCES pantry_locations(location_id) ON DELETE SET NULL,
    product_variant_id UUID NOT NULL REFERENCES product_variants(variant_id) ON DELETE RESTRICT,
    quantity DECIMAL(20,6) NOT NULL CHECK (quantity >= 0),
    unit_of_measure_id UUID NOT NULL REFERENCES units_of_measure(unit_id) ON DELETE RESTRICT,
    purchase_date DATE,
    expiration_date DATE,
    purchase_price DECIMAL(10,2) CHECK (purchase_price >= 0),
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create indexes for inventory_items
CREATE INDEX idx_inventory_items_pantry_id ON inventory_items(pantry_id);
CREATE INDEX idx_inventory_items_location_id ON inventory_items(location_id);
CREATE INDEX idx_inventory_items_product_variant_id ON inventory_items(product_variant_id);
CREATE INDEX idx_inventory_items_unit_of_measure_id ON inventory_items(unit_of_measure_id);
CREATE INDEX idx_inventory_items_expiration_date ON inventory_items(expiration_date);
CREATE INDEX idx_inventory_items_purchase_date ON inventory_items(purchase_date);
CREATE INDEX idx_inventory_items_deleted_at ON inventory_items(deleted_at);

-- Create usage_logs table for tracking consumption
CREATE TABLE usage_logs (
    usage_log_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    inventory_item_id UUID NOT NULL REFERENCES inventory_items(item_id) ON DELETE CASCADE,
    pantry_id UUID NOT NULL REFERENCES pantries(pantry_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    quantity_used DECIMAL(20,6) NOT NULL CHECK (quantity_used > 0),
    unit_of_measure_id UUID NOT NULL REFERENCES units_of_measure(unit_id) ON DELETE RESTRICT,
    usage_date TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for usage_logs
CREATE INDEX idx_usage_logs_inventory_item_id ON usage_logs(inventory_item_id);
CREATE INDEX idx_usage_logs_pantry_id ON usage_logs(pantry_id);
CREATE INDEX idx_usage_logs_user_id ON usage_logs(user_id);
CREATE INDEX idx_usage_logs_usage_date ON usage_logs(usage_date);

-- Create inventory_adjustments table for non-consumption changes
CREATE TABLE inventory_adjustments (
    adjustment_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    inventory_item_id UUID NOT NULL REFERENCES inventory_items(item_id) ON DELETE CASCADE,
    pantry_id UUID NOT NULL REFERENCES pantries(pantry_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    adjustment_type VARCHAR(20) NOT NULL CHECK (adjustment_type IN ('spoilage', 'loss', 'manual_correction', 'transfer_out', 'transfer_in')),
    quantity_adjusted DECIMAL(20,6) NOT NULL, -- Can be negative
    unit_of_measure_id UUID NOT NULL REFERENCES units_of_measure(unit_id) ON DELETE RESTRICT,
    reason TEXT,
    adjustment_date TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for inventory_adjustments
CREATE INDEX idx_inventory_adjustments_inventory_item_id ON inventory_adjustments(inventory_item_id);
CREATE INDEX idx_inventory_adjustments_pantry_id ON inventory_adjustments(pantry_id);
CREATE INDEX idx_inventory_adjustments_user_id ON inventory_adjustments(user_id);
CREATE INDEX idx_inventory_adjustments_adjustment_date ON inventory_adjustments(adjustment_date);
CREATE INDEX idx_inventory_adjustments_type ON inventory_adjustments(adjustment_type);

-- Create triggers for updated_at columns
CREATE TRIGGER update_inventory_items_updated_at 
    BEFORE UPDATE ON inventory_items 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
