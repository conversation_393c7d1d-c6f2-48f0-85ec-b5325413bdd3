basePath: /api/v1
definitions:
  domain.AlertConfigurationRequest:
    properties:
      alert_days:
        maximum: 7
        minimum: 0
        type: integer
      category_filters:
        items:
          type: string
        type: array
      channels:
        items:
          $ref: '#/definitions/domain.NotificationChannel'
        minItems: 1
        type: array
      critical_days:
        maximum: 1
        minimum: 0
        type: integer
      enabled:
        type: boolean
      min_value:
        minimum: 0
        type: number
      quiet_hours:
        $ref: '#/definitions/domain.QuietHours'
      warning_days:
        maximum: 30
        minimum: 1
        type: integer
    required:
    - channels
    type: object
  domain.ChangePasswordRequest:
    properties:
      confirm_password:
        type: string
      current_password:
        type: string
      new_password:
        minLength: 8
        type: string
    required:
    - confirm_password
    - current_password
    - new_password
    type: object
  domain.ConsumeInventoryRequest:
    properties:
      consumed_quantity:
        type: number
      notes:
        maxLength: 500
        type: string
    required:
    - consumed_quantity
    type: object
  domain.CreateInventoryItemRequest:
    properties:
      expiration_date:
        type: string
      location_id:
        type: string
      notes:
        maxLength: 1000
        type: string
      product_variant_id:
        type: string
      purchase_date:
        type: string
      purchase_price:
        minimum: 0
        type: number
      quantity:
        type: number
      unit_of_measure_id:
        type: string
    required:
    - product_variant_id
    - quantity
    - unit_of_measure_id
    type: object
  domain.CreatePantryRequest:
    properties:
      description:
        maxLength: 500
        type: string
      name:
        maxLength: 100
        minLength: 1
        type: string
    required:
    - name
    type: object
  domain.ExpirationTrackingRequest:
    properties:
      alert_days:
        maximum: 7
        minimum: 0
        type: integer
      category_ids:
        items:
          type: string
        type: array
      channels:
        items:
          $ref: '#/definitions/domain.NotificationChannel'
        type: array
      critical_days:
        maximum: 1
        minimum: 0
        type: integer
      send_alerts:
        type: boolean
      warning_days:
        maximum: 30
        minimum: 1
        type: integer
    type: object
  domain.LoginCredentials:
    properties:
      email:
        type: string
      password:
        minLength: 8
        type: string
    required:
    - email
    - password
    type: object
  domain.NotificationChannel:
    enum:
    - email
    - telegram
    - supabase
    - webhook
    - in_app
    type: string
    x-enum-varnames:
    - NotificationChannelEmail
    - NotificationChannelTelegram
    - NotificationChannelSupabase
    - NotificationChannelWebhook
    - NotificationChannelInApp
  domain.QuietHours:
    properties:
      enabled:
        type: boolean
      end_time:
        type: string
      start_time:
        type: string
      timezone:
        type: string
    type: object
  domain.RegisterCredentials:
    properties:
      confirm_password:
        type: string
      email:
        type: string
      first_name:
        maxLength: 100
        type: string
      last_name:
        maxLength: 100
        type: string
      password:
        minLength: 8
        type: string
      username:
        maxLength: 50
        minLength: 3
        type: string
    required:
    - confirm_password
    - email
    - password
    - username
    type: object
  domain.UpdateInventoryItemRequest:
    properties:
      expiration_date:
        type: string
      location_id:
        type: string
      notes:
        maxLength: 1000
        type: string
      purchase_date:
        type: string
      purchase_price:
        minimum: 0
        type: number
      quantity:
        type: number
    required:
    - quantity
    type: object
  domain.UpdatePantryRequest:
    properties:
      description:
        maxLength: 500
        type: string
      name:
        maxLength: 100
        minLength: 1
        type: string
    required:
    - name
    type: object
  domain.UpdateProfileRequest:
    properties:
      first_name:
        maxLength: 100
        type: string
      last_name:
        maxLength: 100
        type: string
      profile_picture_url:
        type: string
    type: object
  handler.APIResponse:
    properties:
      data: {}
      error:
        $ref: '#/definitions/handler.ErrorInfo'
      message:
        type: string
      metadata:
        additionalProperties: true
        type: object
      request_id:
        type: string
      success:
        type: boolean
      timestamp:
        type: string
    type: object
  handler.ErrorInfo:
    properties:
      code:
        type: string
      details:
        additionalProperties: true
        type: object
      message:
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: Pantry Pal API Support
    url: http://www.pantrypal.com/support
  description: A comprehensive multi-tenant pantry management system for tracking
    inventory, managing shopping lists, and reducing food waste.
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: Pantry Pal API
  version: "2.0"
paths:
  /auth/login:
    post:
      consumes:
      - application/json
      description: Authenticate user with email and password
      parameters:
      - description: Login credentials
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.LoginCredentials'
      produces:
      - application/json
      responses:
        "200":
          description: Login successful
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Invalid credentials
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      summary: User login
      tags:
      - Authentication
  /auth/logout:
    post:
      consumes:
      - application/json
      description: Logout user and revoke refresh token
      produces:
      - application/json
      responses:
        "200":
          description: Logout successful
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      summary: User logout
      tags:
      - Authentication
  /auth/refresh:
    post:
      consumes:
      - application/json
      description: Generate new access token using refresh token from cookie
      produces:
      - application/json
      responses:
        "200":
          description: Token refreshed successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Refresh token not found or invalid
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      summary: Refresh access token
      tags:
      - Authentication
  /auth/register:
    post:
      consumes:
      - application/json
      description: Create a new user account with email, username, and password
      parameters:
      - description: Registration credentials
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.RegisterCredentials'
      produces:
      - application/json
      responses:
        "201":
          description: User registered successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "409":
          description: Email or username already exists
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      summary: Register a new user
      tags:
      - Authentication
  /expiration/alerts/global:
    get:
      consumes:
      - application/json
      description: Retrieve expiration alert configuration for a pantry or globally
      produces:
      - application/json
      responses:
        "200":
          description: Alert configuration retrieved successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid pantry ID
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to pantry
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get alert configuration
      tags:
      - Expiration Tracking
    post:
      consumes:
      - application/json
      description: Configure expiration alert settings for a pantry or globally
      parameters:
      - description: Alert configuration
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.AlertConfigurationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Alert configuration saved successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to pantry
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Configure expiration alerts
      tags:
      - Expiration Tracking
  /inventory/{itemId}:
    put:
      consumes:
      - application/json
      description: Update an existing inventory item
      parameters:
      - description: Inventory item ID
        in: path
        name: itemId
        required: true
        type: string
      - description: Updated inventory item data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.UpdateInventoryItemRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Inventory item updated successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to item
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Inventory item not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Update inventory item
      tags:
      - Inventory
  /inventory/{itemId}/consume:
    post:
      consumes:
      - application/json
      description: Consume a specified quantity from an inventory item
      parameters:
      - description: Inventory item ID
        in: path
        name: itemId
        required: true
        type: string
      - description: Consumption data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.ConsumeInventoryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Inventory item consumed successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or insufficient quantity
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to item
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Inventory item not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Consume inventory item
      tags:
      - Inventory
  /pantries:
    get:
      consumes:
      - application/json
      description: Retrieve all pantries accessible to the authenticated user with
        pagination
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10, max: 100)'
        in: query
        name: limit
        type: integer
      - description: 'Only return owned pantries (default: false)'
        in: query
        name: owner_only
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: Pantries retrieved successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid query parameters
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get user's pantries
      tags:
      - Pantries
    post:
      consumes:
      - application/json
      description: Create a new pantry for the authenticated user
      parameters:
      - description: Pantry creation data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.CreatePantryRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Pantry created successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "409":
          description: Pantry name already exists
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Create a new pantry
      tags:
      - Pantries
  /pantries/{pantryId}:
    delete:
      consumes:
      - application/json
      description: Delete a pantry and all its associated data
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Pantry deleted successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid pantry ID
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no delete permission
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Delete pantry
      tags:
      - Pantries
    get:
      consumes:
      - application/json
      description: Retrieve a specific pantry by its ID
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Pantry retrieved successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid pantry ID
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to this pantry
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get pantry by ID
      tags:
      - Pantries
    put:
      consumes:
      - application/json
      description: Update pantry name and description
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Pantry update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.UpdatePantryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Pantry updated successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no edit permission
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "409":
          description: Pantry name already exists
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Update pantry
      tags:
      - Pantries
  /pantries/{pantryId}/expiration/alerts:
    get:
      consumes:
      - application/json
      description: Retrieve expiration alert configuration for a pantry or globally
      parameters:
      - description: Pantry ID (optional for global config)
        in: path
        name: pantryId
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Alert configuration retrieved successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid pantry ID
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to pantry
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get alert configuration
      tags:
      - Expiration Tracking
    post:
      consumes:
      - application/json
      description: Configure expiration alert settings for a pantry or globally
      parameters:
      - description: Pantry ID (optional for global config)
        in: path
        name: pantryId
        type: string
      - description: Alert configuration
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.AlertConfigurationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Alert configuration saved successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to pantry
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Configure expiration alerts
      tags:
      - Expiration Tracking
  /pantries/{pantryId}/expiration/track:
    post:
      consumes:
      - application/json
      description: Track and analyze expiring items in a pantry with configurable
        thresholds
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Expiration tracking configuration
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.ExpirationTrackingRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Expiring items tracked successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to pantry
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Track expiring items
      tags:
      - Expiration Tracking
  /pantries/{pantryId}/inventory:
    get:
      consumes:
      - application/json
      description: Retrieve all inventory items in the specified pantry with pagination
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10, max: 100)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Inventory retrieved successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid pantry ID or query parameters
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to pantry
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get pantry inventory
      tags:
      - Inventory
    post:
      consumes:
      - application/json
      description: Create a new inventory item in the specified pantry
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Inventory item data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.CreateInventoryItemRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Inventory item created successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to pantry
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry or product variant not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Create inventory item
      tags:
      - Inventory
  /users/change-password:
    post:
      consumes:
      - application/json
      description: Change the current authenticated user's password
      parameters:
      - description: Password change data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.ChangePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Password changed successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized or invalid current password
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Change user password
      tags:
      - Users
  /users/profile:
    get:
      consumes:
      - application/json
      description: Retrieve the current authenticated user's profile information
      produces:
      - application/json
      responses:
        "200":
          description: Profile retrieved successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get user profile
      tags:
      - Users
    put:
      consumes:
      - application/json
      description: Update the current authenticated user's profile information
      parameters:
      - description: Profile update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.UpdateProfileRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Profile updated successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Update user profile
      tags:
      - Users
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
