// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "Pantry Pal API Support",
            "url": "http://www.pantrypal.com/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/auth/login": {
            "post": {
                "description": "Authenticate user with email and password",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "User login",
                "parameters": [
                    {
                        "description": "Login credentials",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.LoginCredentials"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Login successful",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid input or validation error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Invalid credentials",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            }
        },
        "/auth/logout": {
            "post": {
                "description": "Logout user and revoke refresh token",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "User logout",
                "responses": {
                    "200": {
                        "description": "Logout successful",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            }
        },
        "/auth/refresh": {
            "post": {
                "description": "Generate new access token using refresh token from cookie",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "Refresh access token",
                "responses": {
                    "200": {
                        "description": "Token refreshed successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Refresh token not found or invalid",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            }
        },
        "/auth/register": {
            "post": {
                "description": "Create a new user account with email, username, and password",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "Register a new user",
                "parameters": [
                    {
                        "description": "Registration credentials",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.RegisterCredentials"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "User registered successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid input or validation error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "409": {
                        "description": "Email or username already exists",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            }
        },
        "/expiration/alerts/global": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieve expiration alert configuration for a pantry or globally",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Expiration Tracking"
                ],
                "summary": "Get alert configuration",
                "responses": {
                    "200": {
                        "description": "Alert configuration retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid pantry ID",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden - no access to pantry",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "404": {
                        "description": "Pantry not found",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Configure expiration alert settings for a pantry or globally",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Expiration Tracking"
                ],
                "summary": "Configure expiration alerts",
                "parameters": [
                    {
                        "description": "Alert configuration",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.AlertConfigurationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Alert configuration saved successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid input or validation error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden - no access to pantry",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "404": {
                        "description": "Pantry not found",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            }
        },
        "/inventory/{itemId}": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update an existing inventory item",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Inventory"
                ],
                "summary": "Update inventory item",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Inventory item ID",
                        "name": "itemId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Updated inventory item data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.UpdateInventoryItemRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Inventory item updated successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid input or validation error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden - no access to item",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "404": {
                        "description": "Inventory item not found",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            }
        },
        "/inventory/{itemId}/consume": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Consume a specified quantity from an inventory item",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Inventory"
                ],
                "summary": "Consume inventory item",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Inventory item ID",
                        "name": "itemId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Consumption data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.ConsumeInventoryRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Inventory item consumed successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid input or insufficient quantity",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden - no access to item",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "404": {
                        "description": "Inventory item not found",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            }
        },
        "/pantries": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieve all pantries accessible to the authenticated user with pagination",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Pantries"
                ],
                "summary": "Get user's pantries",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Page number (default: 1)",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Items per page (default: 10, max: 100)",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "Only return owned pantries (default: false)",
                        "name": "owner_only",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Pantries retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid query parameters",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a new pantry for the authenticated user",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Pantries"
                ],
                "summary": "Create a new pantry",
                "parameters": [
                    {
                        "description": "Pantry creation data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.CreatePantryRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Pantry created successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid input or validation error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "409": {
                        "description": "Pantry name already exists",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            }
        },
        "/pantries/{pantryId}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieve a specific pantry by its ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Pantries"
                ],
                "summary": "Get pantry by ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Pantry ID",
                        "name": "pantryId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Pantry retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid pantry ID",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden - no access to this pantry",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "404": {
                        "description": "Pantry not found",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update pantry name and description",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Pantries"
                ],
                "summary": "Update pantry",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Pantry ID",
                        "name": "pantryId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Pantry update data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.UpdatePantryRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Pantry updated successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid input or validation error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden - no edit permission",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "404": {
                        "description": "Pantry not found",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "409": {
                        "description": "Pantry name already exists",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete a pantry and all its associated data",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Pantries"
                ],
                "summary": "Delete pantry",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Pantry ID",
                        "name": "pantryId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Pantry deleted successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid pantry ID",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden - no delete permission",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "404": {
                        "description": "Pantry not found",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            }
        },
        "/pantries/{pantryId}/expiration/alerts": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieve expiration alert configuration for a pantry or globally",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Expiration Tracking"
                ],
                "summary": "Get alert configuration",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Pantry ID (optional for global config)",
                        "name": "pantryId",
                        "in": "path"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Alert configuration retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid pantry ID",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden - no access to pantry",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "404": {
                        "description": "Pantry not found",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Configure expiration alert settings for a pantry or globally",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Expiration Tracking"
                ],
                "summary": "Configure expiration alerts",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Pantry ID (optional for global config)",
                        "name": "pantryId",
                        "in": "path"
                    },
                    {
                        "description": "Alert configuration",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.AlertConfigurationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Alert configuration saved successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid input or validation error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden - no access to pantry",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "404": {
                        "description": "Pantry not found",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            }
        },
        "/pantries/{pantryId}/expiration/track": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Track and analyze expiring items in a pantry with configurable thresholds",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Expiration Tracking"
                ],
                "summary": "Track expiring items",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Pantry ID",
                        "name": "pantryId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Expiration tracking configuration",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.ExpirationTrackingRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Expiring items tracked successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid input or validation error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden - no access to pantry",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "404": {
                        "description": "Pantry not found",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            }
        },
        "/pantries/{pantryId}/inventory": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieve all inventory items in the specified pantry with pagination",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Inventory"
                ],
                "summary": "Get pantry inventory",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Pantry ID",
                        "name": "pantryId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "Page number (default: 1)",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Items per page (default: 10, max: 100)",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Inventory retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid pantry ID or query parameters",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden - no access to pantry",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "404": {
                        "description": "Pantry not found",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a new inventory item in the specified pantry",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Inventory"
                ],
                "summary": "Create inventory item",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Pantry ID",
                        "name": "pantryId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Inventory item data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.CreateInventoryItemRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Inventory item created successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid input or validation error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden - no access to pantry",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "404": {
                        "description": "Pantry or product variant not found",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            }
        },
        "/users/change-password": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Change the current authenticated user's password",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Change user password",
                "parameters": [
                    {
                        "description": "Password change data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.ChangePasswordRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Password changed successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid input or validation error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized or invalid current password",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            }
        },
        "/users/profile": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieve the current authenticated user's profile information",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Get user profile",
                "responses": {
                    "200": {
                        "description": "Profile retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update the current authenticated user's profile information",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Update user profile",
                "parameters": [
                    {
                        "description": "Profile update data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/domain.UpdateProfileRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Profile updated successfully",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid input or validation error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/handler.APIResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "domain.AlertConfigurationRequest": {
            "type": "object",
            "required": [
                "channels"
            ],
            "properties": {
                "alert_days": {
                    "type": "integer",
                    "maximum": 7,
                    "minimum": 0
                },
                "category_filters": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "channels": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "$ref": "#/definitions/domain.NotificationChannel"
                    }
                },
                "critical_days": {
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                },
                "enabled": {
                    "type": "boolean"
                },
                "min_value": {
                    "type": "number",
                    "minimum": 0
                },
                "quiet_hours": {
                    "$ref": "#/definitions/domain.QuietHours"
                },
                "warning_days": {
                    "type": "integer",
                    "maximum": 30,
                    "minimum": 1
                }
            }
        },
        "domain.ChangePasswordRequest": {
            "type": "object",
            "required": [
                "confirm_password",
                "current_password",
                "new_password"
            ],
            "properties": {
                "confirm_password": {
                    "type": "string"
                },
                "current_password": {
                    "type": "string"
                },
                "new_password": {
                    "type": "string",
                    "minLength": 8
                }
            }
        },
        "domain.ConsumeInventoryRequest": {
            "type": "object",
            "required": [
                "consumed_quantity"
            ],
            "properties": {
                "consumed_quantity": {
                    "type": "number"
                },
                "notes": {
                    "type": "string",
                    "maxLength": 500
                }
            }
        },
        "domain.CreateInventoryItemRequest": {
            "type": "object",
            "required": [
                "product_variant_id",
                "quantity",
                "unit_of_measure_id"
            ],
            "properties": {
                "expiration_date": {
                    "type": "string"
                },
                "location_id": {
                    "type": "string"
                },
                "notes": {
                    "type": "string",
                    "maxLength": 1000
                },
                "product_variant_id": {
                    "type": "string"
                },
                "purchase_date": {
                    "type": "string"
                },
                "purchase_price": {
                    "type": "number",
                    "minimum": 0
                },
                "quantity": {
                    "type": "number"
                },
                "unit_of_measure_id": {
                    "type": "string"
                }
            }
        },
        "domain.CreatePantryRequest": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "description": {
                    "type": "string",
                    "maxLength": 500
                },
                "name": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 1
                }
            }
        },
        "domain.ExpirationTrackingRequest": {
            "type": "object",
            "properties": {
                "alert_days": {
                    "type": "integer",
                    "maximum": 7,
                    "minimum": 0
                },
                "category_ids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "channels": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/domain.NotificationChannel"
                    }
                },
                "critical_days": {
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                },
                "send_alerts": {
                    "type": "boolean"
                },
                "warning_days": {
                    "type": "integer",
                    "maximum": 30,
                    "minimum": 1
                }
            }
        },
        "domain.LoginCredentials": {
            "type": "object",
            "required": [
                "email",
                "password"
            ],
            "properties": {
                "email": {
                    "type": "string"
                },
                "password": {
                    "type": "string",
                    "minLength": 8
                }
            }
        },
        "domain.NotificationChannel": {
            "type": "string",
            "enum": [
                "email",
                "telegram",
                "supabase",
                "webhook",
                "in_app"
            ],
            "x-enum-varnames": [
                "NotificationChannelEmail",
                "NotificationChannelTelegram",
                "NotificationChannelSupabase",
                "NotificationChannelWebhook",
                "NotificationChannelInApp"
            ]
        },
        "domain.QuietHours": {
            "type": "object",
            "properties": {
                "enabled": {
                    "type": "boolean"
                },
                "end_time": {
                    "type": "string"
                },
                "start_time": {
                    "type": "string"
                },
                "timezone": {
                    "type": "string"
                }
            }
        },
        "domain.RegisterCredentials": {
            "type": "object",
            "required": [
                "confirm_password",
                "email",
                "password",
                "username"
            ],
            "properties": {
                "confirm_password": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "first_name": {
                    "type": "string",
                    "maxLength": 100
                },
                "last_name": {
                    "type": "string",
                    "maxLength": 100
                },
                "password": {
                    "type": "string",
                    "minLength": 8
                },
                "username": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 3
                }
            }
        },
        "domain.UpdateInventoryItemRequest": {
            "type": "object",
            "required": [
                "quantity"
            ],
            "properties": {
                "expiration_date": {
                    "type": "string"
                },
                "location_id": {
                    "type": "string"
                },
                "notes": {
                    "type": "string",
                    "maxLength": 1000
                },
                "purchase_date": {
                    "type": "string"
                },
                "purchase_price": {
                    "type": "number",
                    "minimum": 0
                },
                "quantity": {
                    "type": "number"
                }
            }
        },
        "domain.UpdatePantryRequest": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "description": {
                    "type": "string",
                    "maxLength": 500
                },
                "name": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 1
                }
            }
        },
        "domain.UpdateProfileRequest": {
            "type": "object",
            "properties": {
                "first_name": {
                    "type": "string",
                    "maxLength": 100
                },
                "last_name": {
                    "type": "string",
                    "maxLength": 100
                },
                "profile_picture_url": {
                    "type": "string"
                }
            }
        },
        "handler.APIResponse": {
            "type": "object",
            "properties": {
                "data": {},
                "error": {
                    "$ref": "#/definitions/handler.ErrorInfo"
                },
                "message": {
                    "type": "string"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "request_id": {
                    "type": "string"
                },
                "success": {
                    "type": "boolean"
                },
                "timestamp": {
                    "type": "string"
                }
            }
        },
        "handler.ErrorInfo": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "details": {
                    "type": "object",
                    "additionalProperties": true
                },
                "message": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "description": "Type \"Bearer\" followed by a space and JWT token.",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "2.0",
	Host:             "localhost:8080",
	BasePath:         "/api/v1",
	Schemes:          []string{},
	Title:            "Pantry Pal API",
	Description:      "A comprehensive multi-tenant pantry management system for tracking inventory, managing shopping lists, and reducing food waste.",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
